# Configuration file
@{
    SocqUser    = 'socq-purecloud'
    # To encrypt creds: Import-Module ..\modules\SDSecretManagement; Protect-SDSecret 'pass'
    SocqPass    = 'generate using command above'
    # The SOC host name, i.e. pcc-mssql-f1.du. If set to $null, SOC reporting will be disabled.
    SocHostname = $null
    AutoFixRepos =
    @(
        # Add a list of the repository names that should automatically be fixed.
        # i.e. 'service-desk-tools'
    )
}
