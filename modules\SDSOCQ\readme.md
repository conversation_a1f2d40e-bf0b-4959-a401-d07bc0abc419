# SDSOCQ

PowerShell cmdlet interface for the SOCQ API.

| Cmdlet                     | Description                                                                      |
|----------------------------|----------------------------------------------------------------------------------|
| Add-SDSOCQServiceResult    | Add a service result to the queue to be submitted to the server.                 |
| Clear-SDSOCQServiceResult  | Clear all queued service results that have not yet been submitted to the server. |
| Get-SDSOCQServiceResult    | Get all service results pending submission to the server.                        |
| Initialize-SDSOCQ          | Prepare the module for use.                                                      |
| Register-SDSOCQService     | Create a new service definition.                                                 |
| Remove-SDSOCQServiceResult | Remove a service result from the queue to be submitted to the server.            |
| Set-SDSOCQServiceResult    | Immediately submit a service result to the server.                               |
| Submit-SDSOCQServiceResult | Submit all queued service results to the server.                                 |
| Unregister-SDSOCQService   | Delete an existing service definition.                                           |
