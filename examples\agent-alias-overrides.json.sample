{"_comment": "Agent <PERSON><PERSON> Override Configuration", "_description": "This file contains custom agent alias mappings for Genesys Cloud users. Keys must be valid Genesys Cloud user IDs (GUIDs) and values are the desired agent alias names.", "_usage": "Use with: .\\batch\\GENESYS-AGENT-ALIASES.ps1 -AliasOverrideFile .\\examples\\agent-alias-overrides.json", "_examples": {"4edab032-8084-46a0-bf59-b27fa1bb4fac": "<PERSON>", "a1b2c3d4-5678-90ab-cdef-123456789012": "<PERSON><PERSON>", "f9a43256-ed82-46ef-98e0-0ce499b23c89": "<PERSON>"}, "_notes": ["Remove the underscore-prefixed fields above before using this file", "User IDs must be valid GUIDs in the format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx", "Alias names cannot be empty or contain only whitespace", "If a user ID is not found in this file, the script will use the first name from their display name", "Invalid entries will be logged as warnings and skipped"]}