{
    "cSpell.allowCompoundWords": true,
    "cSpell.diagnosticLevel": "Information",
    "cSpell.enableFiletypes": [
        "powershell"
    ],
    "cSpell.ignoreWords": [
        "cjisa",
        "Realtimev",
        "sdesk",
        "SDSOCQ",
        "SDDBMSSQL",
        "Socq",
        "SDGC",
        "SDMSSQL"
    ],
    "cSpell.words": [
        "<PERSON><PERSON><PERSON>",
        "Genesys",
        "ISDN",
        "PureCloud",
        "Sonus"
    ],
    // Use a custom PowerShell Script Analyzer settings file for this workspace.
    // Relative paths for this setting are always relative to the workspace root dir.
    "powershell.scriptAnalysis.settingsPath": "./PSScriptAnalyzerSettings.psd1"
}