[CmdletBinding(SupportsShouldProcess = $false)]
[System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("PSUseCompatibleCommands", "")]
Param()

$modules = Get-ChildItem -LiteralPath:(Split-Path -Path:$PSScriptRoot -Parent) -Filter '*.psm1'
foreach ( $module in $modules )
{
    'Importing module "{0}"...' -f $module.BaseName | Out-Default
    if ( @(Get-Module $module.BaseName).Count -gt 0 )
    {
        Remove-Module -Name:$module.BaseName -Force
    }
    Import-Module -Name:$module.FullName -Force
}

$params =
@{
    SocServerHost = 'soc-dev.deakin.edu.au'
    DefaultHostName = 'gitlab-ci-cd'
    DefaultServiceName = 'sdsocq'
    Credential = [PSCredential]::Empty
}
Initialize-SDSOCQ @params

Describe 'Add-SDSOCQServiceResult' {
    AfterEach {
        Clear-SDSOCQServiceResult
    }

    Context 'Add a single result' {
        It 'single result queued for submission' {
            Add-SDSOCQServiceResult -Status:'Warning' -Output:'test'
            @(Get-SDSOCQServiceResult).Count | Should -Be 1
        }

        It 'result should be warning status' {
            Add-SDSOCQServiceResult -Status:'Warning' -Output:'test'
            @(Get-SDSOCQServiceResult)[0].Status | Should -Be 'Warning'
        }
    }
}