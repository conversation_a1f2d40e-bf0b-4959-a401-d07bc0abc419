<#
.SYNOPSIS

Execute the Genesys Cloud BI adapter and report status to SOC.

.DESCRIPTION

A wrapper script that provides logging and SOC reporting for the UCA Genesys
Cloud BI adapter.

Called by scheduled tasks in the "\Service Desk Prod\" and "\Service Desk Dev\"
folders in the Windows task scheduler.

To get a list of the scheduled tasks:
    Get-ScheduledTask -TaskPath:'\Service Desk *\'

Development
    Development can be performed on any workstation. Keep the PowerShell version
    the same as the server operating system (currently 5.1 on Server 2019).

    The following code will make some directories in a temporary folder to use
    for development. Take the ExePath and LogPath and update the local
    run-bi-adapter.psd1 file with them.

        $platforms = @(
            'GenesysCloud'
            'CallJourney'
        )
        $tasks = @(
            'Adherence',
            'Aggregation',
            'CallJourney',
            'Chat',
            'Evaluation',
            'EvaluationCatchUp',
            'FactData',
            'Interaction',
            'PresenceDetail',
            'Realtime',
            'SubsUsers',
            'Subscription',
            'TimeOffReq',
            'VoiceAnalysis,
            'WFMSchedule'
        )
        $basePath = Join-Path -Path:$env:TEMP -ChildPath:'BI-Adapters'
        $logPath = Join-Path -Path:$basePath -ChildPath:'Logs'
        if ( (Test-Path -Path:$logPath) -eq $false )
        {
            New-Item -Path:$logPath -ItemType:Directory -Verbose | Out-Null
        }
        foreach ( $platform in $platforms )
        {
            $platformBase = Join-Path -Path:$basePath -ChildPath:$platform
            $prodPath = Join-Path -Path:$platformBase -ChildPath:'Prod'
            $devPath = Join-Path -Path:$platformBase -ChildPath:'Dev'
            foreach ( $p in @($prodPath, $devPath) )
            {
                if ( (Test-Path -Path:$p) -eq $false )
                {
                    New-Item -Path:$p -ItemType:Directory -Verbose | Out-Null
                }
                foreach ( $e in @('RealtimeFeed.exe', 'GCPowerBI.exe', 'CallJourneySync.exe') )
                {
                    $tp = Join-Path -Path:$p -ChildPath:$e
                    Copy-Item -Path:'c:\windows\system32\sort.exe' -Destination:$tp -Verbose
                }
                foreach ( $t in $tasks )
                {
                    $fp = Join-Path -Path:$p -ChildPath:$t
                    "Task: $t, rename this file to cause an error with this task" | Out-File -FilePath:$fp
                }
            }
        }
        Write-Output 'Update configuration with:'
        Write-Output "ExeBase: $basePath"
        Write-Output "LogPath: $logPath"

#>
[CmdletBinding(SupportsShouldProcess = $false)]
Param
(
    # Which Genesys environment to run against
    [Parameter()]
    [ValidateSet('Dev', 'Prod')]
    [string]
    $Environment = "Dev",

    # What type of task to run
    [Parameter()]
    [ValidateSet(
        'Adherence',
        'Aggregation',
        'Chat',
        'Evaluation',
        'EvaluationCatchUp',
        'FactData',
        'HeadCountForecast',
        'HoursBlockedData',
        'Interaction',
        'OauthUsage',
        'PresenceDetail',
        'OfferedForecast',
        'Realtime',
        'ScheduleDetails',
        'Subscription',
        'SubsUsers',
        'TimeOffReq',
        'VoiceAnalysis',
        'WFMAudit',
        'WFMSchedule'
    )]
    [string]
    $Task = 'Aggregation',

    # Should the task be repeated after a period of time? 0 = no. This is
    # required for tasks that run on an interval less than 1 minute as that is
    # the smallest period for the Windows task scheduler. Only will repeat until
    # one minute in total has passed. Passing 15 would result in 4 runs. 30 in
    # two runs.
    [Parameter()]
    [int]
    $RepeatEverySeconds = 0
)

#Requires -Version:5
Set-StrictMode -Version:5
$ErrorActionPreference = 'Stop'
Import-Module -Name:"${PSScriptRoot}\..\modules\SDSecretManagement" -Verbose:$false

$config = Import-LocalizedData

switch ( $Task )
{
    'CallJourney'   { $platform = $Task }
    default         { $platform = 'GenesysCloud' }
}

$exeBase = Join-Path -Path:$config.ExeBase -ChildPath:$platform
$exeBase = Join-Path -Path:$exeBase -ChildPath:$Environment
Set-Location -Path:$exeBase

$logPath = "{0}-{1}-{2}.log" -f
    $Environment,
    $Task,
    ([DateTime]::Now.ToString('yyyy-MM-dd'))

$logPath = Join-Path -Path:$config.LogPath -ChildPath:$logPath

$exeName = 'GenesysAdapter.exe'
$socPrefix = 'PURECLOUD-BI'
switch ( $Task )
{
    'CallJourney'
    {
        $exeName = 'CallJourneySync.exe'
        $socPrefix = 'BI'
    }
}
$exePath = Join-Path -Path:$exeBase -ChildPath:$exeName

Start-Transcript -Path:$logPath -Append
try
{
    $socCreds = Unprotect-SDSecret -Content:$config.SocqPass -UserName:$config.SocqUser

    # Submit an OK for long running tasks as the script starts, this results
    # in a quicker clearing of SOC as the jobs only report to SOC on exit.
    if ( $Task -in 'Realtime' )
    {
        $socResult = $config.SocResultBody.Clone()
        $socResult.service_description = $socPrefix + '-' + $Environment.ToUpper() + '-' + $Task.ToUpper()
        $socResult = $socResult | ConvertTo-Json
        $socResult | Out-Default
        $params =
        @{
            Uri         = $config.SocBaseUri + "/result/services"
            Method      = 'POST'
            Body        = $socResult
            Credential  = $socCreds
            ContentType = 'application/json'
        }
        Invoke-RestMethod @params | Out-Null
    }

    $executionTimer = [System.Diagnostics.Stopwatch]::StartNew()
    do
    {
        $runTimer = [System.Diagnostics.Stopwatch]::StartNew()
        $msg = (
            "{0} Running {1} {2}" -f
                ([DateTime]::Now.ToString('s')),
                $exePath,
                $Task
        )
        Write-Output $msg
        & $exePath Job=$Task | Out-Default
        $exitCode = $LASTEXITCODE
        $runFailed = $exitCode -ne 0
        $exitCodeStr = '0x{0:x}' -f $LASTEXITCODE
        $msg = (
            "{0} Exit code {1}, running time {2}" -f
                ([DateTime]::Now.ToString('s')),
                $exitCodeStr,
                $runTimer.Elapsed.ToString()
        )
        Write-Output $msg

        $socResult = $config.SocResultBody.Clone()
        $socResult.status_code = 0
        $socResult.service_description = $socPrefix + '-' + $Environment.ToUpper() + '-' + $Task.ToUpper()
        $socResult.plugin_output = (
            "{0} task completed with exit code {1}, running time {2}" -f
                $Task,
                $exitCodeStr,
                $runTimer.Elapsed.ToString()
        )
        $warnAfterSecs = [int]::MaxValue
        if (
            $config.ContainsKey('ExecutionTimeWarningSec') -and
            $config.ExecutionTimeWarningSec -is [HashTable] -and
            $config.ExecutionTimeWarningSec.ContainsKey($Environment) -and
            $config.ExecutionTimeWarningSec[$Environment].ContainsKey($Task)
        )
        {
            $warnAfterSecs = $config.ExecutionTimeWarningSec[$Environment][$Task]
            $socResult.plugin_output += ' (expect less than ' + [TimeSpan]::FromSeconds($warnAfterSecs).ToString() + ')'
        }
        if ( $runFailed )
        {
            # Critical
            $socResult.status_code = 2
        }
        elseif ( $runTimer.Elapsed.TotalSeconds -gt $warnAfterSecs )
        {
            # Warning
            $socResult.status_code = 1
        }

        $socResult = $socResult | ConvertTo-Json
        $socResult | Out-Default
        $params =
        @{
            Uri         = $config.SocBaseUri + "/result/services"
            Method      = 'POST'
            Body        = $socResult
            Credential  = $socCreds
            ContentType = 'application/json'
        }
        Invoke-RestMethod @params | Out-Null

        if ( $RepeatEverySeconds -gt 0 )
        {
            $wait = [TimeSpan]::FromSeconds($RepeatEverySeconds) - $runTimer.Elapsed
            if ( $wait.TotalMilliseconds -gt 0 )
            {
                if ( $executionTimer.Elapsed.Add($wait).TotalSeconds -ge 60 )
                {
                    break
                }
                Write-Output "Sleeping $wait"
                Start-Sleep -Milliseconds:$wait.TotalMilliseconds
            }
        }
        else
        {
            break
        }
    }
    while ( $RepeatEverySeconds -gt 0 -and $executionTimer.Elapsed.TotalSeconds -lt 60 )
}
finally
{
    Stop-Transcript
}
