<#
.SYNOPSIS

Create symbolic links for any PowerShell Desired State configuration files

.DESCRIPTION

Looks for any folder called "dsc" under c:\deakin\sdesk and creates symbolic
links in the Systems Unit DSC configuration directories.

Configuration name must start with "sdesk_"

#>
[CmdletBinding(SupportsShouldProcess = $false)]
Param
(
)

#Requires -Version 5.0
$script:ErrorActionPreference = 'Stop'
Set-StrictMode -Version 5

$baseDirSdesk = 'c:\deakin\sdesk'
$baseDirDSC = 'C:\deakin\config\deakin-meta\local\'

if ( Test-Path -LiteralPath:$baseDirSdesk )
{
    $files =
    @(
        Get-ChildItem -LiteralPath:$baseDirSdesk -Recurse -Directory -Filter:'dsc' -Force |
            Get-ChildItem -File -Filter '*.ps1' -Force
    )
    :nextFile foreach ( $file in $files )
    {
        $configName = $null
        $configFirstLine = Get-Content -LiteralPath:$file.FullName -ReadCount:1 -TotalCount:1
        if ( $configFirstLine -match '^Configuration\s+(sdesk_[a-z_]+)$' )
        {
            $configName = $matches[1]
        }
        if ( $null -ne $configName )
        {
            if ( $file.BaseName -match '-for-(.+)$' )
            {
                $targetHost = $matches[1].ToLower()
                if ( -not [System.Net.DNS]::GetHostName().ToLower().Contains($targetHost) )
                {
                    continue nextFile
                }
            }
            $linkName = '{0}.ps1' -f $configName
            $linkPath = Join-Path -Path:$baseDirDSC -ChildPath:$linkName
            $linkTarget = $file.FullName
            if ( -not (Test-Path -LiteralPath:$baseDirDSC) )
            {
                New-Item -Path:$baseDirDSC -ItemType:'Directory' | Out-Null
            }
            if ( -not (Test-Path -LiteralPath:$linkPath) )
            {
                New-Item -ItemType:'SymbolicLink' -Path:$linkPath -Target:$linkTarget -Verbose | Out-Null
            }
        }
    }
}
