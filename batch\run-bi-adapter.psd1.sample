# Configuration file
@{
    ExeBase     = 'C:\deakin\sdesk\bi-adapters'
    LogPath     = 'C:\logs\local\bi-adapters'
    SocqUser    = 'socq-purecloud'
    # To encrypt creds: Import-Module ..\modules\SDSecretManagement; Protect-SDSecret 'pass'
    SocqPass    = 'generate using command above'
    SocBaseUri  = 'https://soc.deakin.edu.au/socq/api/v0.01'
    SocResultBody =
    @{
        host_name           = 'pcc-mssql-f1.du'
        service_description = ''
        status_code         = 0
        plugin_output       = "No errors detected"
    }
    ExecutionTimeWarningSec =
    @{
        Dev =
        @{
            Aggregation     = 60        # Runs every minute
            Chat            = 60
            Evaluation      = 30 * 60
            FactData        = 60 * 60   # Runs every hour
            Interaction     = 120       # Runs every 2 minutes
        }
        Prod =
        @{
            Aggregation     = 60        # Runs every minute
            Chat            = 60
            Evaluation      = 30 * 60
            FactData        = 60 * 60   # Runs every hour
            Interaction     = 120       # Runs every 2 minutes
        }
    }
}
