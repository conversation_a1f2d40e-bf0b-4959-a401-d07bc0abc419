# Configuration file
@{
    SocqUser    = 'socq-purecloud'
    # To encrypt creds: Import-Module ..\modules\SDSecretManagement; Protect-SDSecret 'pass'
    SocqPass    = 'generate using command above'
    SocBaseUri  = 'https://soc.deakin.edu.au/socq/api/v0.01'

    Databases   =
    @{
        'GenesysHistoricalData' =
        @{
            SocService  = 'PURECLOUD-BI-PROD-REALTIME'
            WarningLag  = '00:10:00'
            CriticalLag = '00:30:00'
        }
        'GenesysHistoricalDataDev' =
        @{
            SocService  = 'PURECLOUD-BI-DEV-REALTIME'
            WarningLag  = '01:00:00'
            CriticalLag = '10675199.02:48:05.4775807' # No critical alert in dev
        }
    }
    Queries =
    @{
        'UserRealTimeData'     = 'SELECT MAX([updated]) AS UserRealTimeData     FROM [userRealTimeData]     WITH(NOLOCK)'
        'QueueRealTimeData'    = 'SELECT MAX([updated]) AS QueueRealTimeData    FROM [queueRealTimeData]    WITH(NOLOCK)'
        'UserRealTimeConvData' = 'SELECT MAX([updated]) AS UserRealTimeConvData FROM [userRealTimeConvData] WITH(NOLOCK)'
    }
}
