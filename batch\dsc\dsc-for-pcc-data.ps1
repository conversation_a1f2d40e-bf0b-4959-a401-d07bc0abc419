Configuration sdesk_purecloud_scripts
{
    Import-DscResource -ModuleName:'ComputerManagementDsc' -ModuleVersion:'8.5.0'

    $taskPriorityProd = 6
    # $taskPriorityDev = 8

    #region SOC checks
    $taskName = 'GIT-STATUS SOC check'
    ScheduledTask $taskName
    {
        Ensure                  = 'Present'
        TaskName                = $taskName
        TaskPath                = '\Service Desk Prod\'
        ActionWorkingPath       = 'C:\deakin\sdesk\purecloud-scripts\soc-checks'
        Description             = 'https://wiki.deakin.edu.au/x/nx1rDg'
        ActionExecutable        = 'C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe'
        ActionArguments         = '-file "C:\deakin\sdesk\purecloud-scripts\soc-checks\GIT-STATUS.ps1" -ReportStatus'
        ScheduleType            = 'Once'
        StartTime               = [DateTime]'2016-01-01 04:00' + (Get-DknRandomOffset -MaxOffset:'1:00' -Name:$taskName)
        RepeatInterval          = '02:00:00'
        RepetitionDuration      = 'Indefinitely'
        MultipleInstances       = 'IgnoreNew'
        Priority                = $taskPriorityProd
        Compatibility           = 'Win8'
    }

    ScheduledTask 'purecloud-provisioning-agents'
    {
        TaskPath                   = $Node.ScheduledTaskBasePath
        TaskName                   = 'purecloud-provisioning-agents'
        Description                = 'Provisions PureCloud agents'
        Ensure                     = 'Present'
        Priority                   = 7
        RunLevel                   = 'Highest'
        Compatibility              = 'Win8'
        ScheduleType               = 'Daily'
        DaysInterval               = 1
        StartTime                  = '2019-10-09 04:15:00'
        RepeatInterval             = '00:30:00'
        RepetitionDuration         = '17:00:00'
        Enable                     = $false
        ActionExecutable           = 'c:\windows\system32\WindowsPowerShell\v1.0\powershell.exe'
        ActionArguments            = '-NonInteractive -File C:\deakin\sdesk\purecloud-scripts\batch\PURECLOUD-PROVISIONING-AGENTS.ps1'
        AllowStartIfOnBatteries    = $true
        DontStopIfGoingOnBatteries = $true
        RunOnlyIfNetworkAvailable  = $true
        DisallowDemandStart        = $false
        DisallowHardTerminate      = $false
        ExecutionTimeLimit         = '3'
        MultipleInstances          = 'IgnoreNew'
    }

    ScheduledTask 'purecloud-provisioning-contacts'
    {
        TaskPath                   = $Node.ScheduledTaskBasePath
        TaskName                   = 'purecloud-provisioning-contacts'
        Description                = 'something something contacts'
        Ensure                     = 'Present'
        Priority                   = 7
        RunLevel                   = 'Highest'
        Compatibility              = 'Win8'
        ScheduleType               = 'Daily'
        DaysInterval               = 1
        StartTime                  = '2019-10-09 23:50:00'
        RandomDelay                = '01:00:00'
        Enable                     = $true
        ActionExecutable           = 'c:\windows\system32\WindowsPowerShell\v1.0\powershell.exe'
        ActionArguments            = '-NonInteractive -File C:\deakin\sdesk\purecloud-scripts\batch\PURECLOUD-PROVISIONING-CONTACTS.ps1'
        AllowStartIfOnBatteries    = $true
        DontStopIfGoingOnBatteries = $true
        RunOnlyIfNetworkAvailable  = $true
        DisallowDemandStart        = $false
        DisallowHardTerminate      = $false
        ExecutionTimeLimit         = '3'
        MultipleInstances          = 'IgnoreNew'
    }
    #endregion
}
