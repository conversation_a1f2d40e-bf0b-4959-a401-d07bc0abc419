stages:
  - lint
  - test

lint-test-job:
  stage: lint
  image:
    # https://hub.docker.com/_/microsoft-powershell
    #name: "mcr.microsoft.com/powershell:latest"
    # https://hub.docker.com/_/microsoft-powershell-test-deps
    name: "mcr.microsoft.com/powershell/test-deps:ubuntu-18.04"
  script:
    - echo "Running lint tests..."
    - pwsh -noninteractive -file build/lint-test-job.ps1

unit-test-job:
  stage: test
  image:
    name: "mcr.microsoft.com/powershell/test-deps:ubuntu-18.04"
  script:
    - echo "Running unit tests..."
    - pwsh -noninteractive -file build/unit-test-job.ps1
  artifacts:
    paths:
      - build/unit-test-job.xml
    expire_in: 1 month
    reports:
      junit: build/unit-test-job.xml
