<#
.DESCRIPTION
    Configures PureCloud agents into roles and queues. Creates and assigns phones.

    API documentation:  https://developer.mypurecloud.com.au/api/rest/v2/
    API explorer:       https://developer.mypurecloud.com.au/developer-tools/#/api-explorer

    Sys - Troubleshooting - PureCloud Contact Centre        https://wiki.deakin.edu.au/x/cb6lBg

    Current list of calls made by this script, and required permissions
        GET     /api/v2/architect/ivrs                                                      telephony:plugin:all
        GET     /api/v2/authorization/roles                                                 authorization:role:view
        GET     /api/v2/authorization/roles/{0}/users                                       Not documented
        PUT     /api/v2/authorization/roles/{0}/users/add                                   authorization:grant:add
        PUT     /api/v2/authorization/roles/{0}/users/remove                                authorization:grant:delete
        POST    /api/v2/groups                                                              directory:group:add
        DELETE  /api/v2/groups/${groupId}                                                   directory:group:delete
        DELETE  /api/v2/groups/${groupId}/members
        GET     /api/v2/groups/${groupId}/members
        POST    /api/v2/groups/${groupId}/members
        GET     /api/v2/routing/languages
        GET     /api/v2/routing/queues                                                      routing:queue:view
        GET     /api/v2/routing/queues/{0}/users                                            routing:queue:view
        POST    /api/v2/routing/queues/{0}/users?delete=false                               routing:queue:edit
        POST    /api/v2/routing/queues/{0}/users?delete=true                                routing:queue:edit
        GET     /api/v2/routing/skills
        POST    /api/v2/search
        GET     /api/v2/telephony/providers/edges/phonebasesettings                         telephony:plugin:all
        GET     /api/v2/telephony/providers/edges/phones                                    telephony:plugin:all
        POST    /api/v2/telephony/providers/edges/phones                                    telephony:plugin:all
        GET     /api/v2/telephony/providers/edges/phones/template?phoneBaseSettingsId={0}   telephony:plugin:all
        DELETE  /api/v2/telephony/providers/edges/phones/{0}                                telephony:plugin:all
        GET     /api/v2/telephony/providers/edges/sites                                     None documented
        POST    /api/v2/users/
        GET     /api/v2/users/                                                              None documented
        DELETE  /api/v2/users/{0}
        GET     /api/v2/users/{0}
        PATCH   /api/v2/users/{0}
        GET     /api/v2/users/{0}/routinglanguages
        POST    /api/v2/users/{0}/routinglanguages
        GET     /api/v2/users/{0}/routingskills
        POST    /api/v2/users/{0}/routingskills
        DELETE  /api/v2/users/{0}/routingskills/{0}
        PUT     /api/v2/users/{0}/station/defaultstation/{1}                                telephony:plugin:all OR telephony:phone:assign
        POST    https://apps.mypurecloud.com.au/platform/api/v2/search                      Not documented

#>
[Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidLongLines', "")]
[Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSUseDeclaredVarsMoreThanAssignments', "")]
[CmdletBinding(SupportsShouldProcess = $true)]
Param
(
    [Parameter()]
    [switch]
    $FullDiff,

    [Parameter()]
    [switch]
    $Force
)

#Requires -Version 4.0
$script:ErrorActionPreference = 'Stop'
Set-StrictMode -Version 4

try
{
    . 'C:\scripts\includes\Common-WindowsFunctions.ps1'
    . 'C:\scripts\soc_local\LocalCheck.ps1'

    Start-DknScriptLogging -Confirm:$false

    $AccessGroupsAreLike = 'wg-purecloud*'
    # $ComputerGroupName = 'wgsw-sccm-purecloud-agent-computers'
    $WebRtcPhoneBaseSettingsName = 'PureCloud WebRTC'
    $WebRtcNameSuffix = ' WebRTC Phone (Auto)'
    # $SoftphoneBaseSettingsName = 'SoftPhone'
    # $SoftphoneNameSuffix = ' Softphone (Auto)'
    # $RemotePhoneBaseSettingsName = 'Deakin-SkypePhone'
    # $RemotePhoneNameSuffix = ' Skype Phone (Auto)'
    $SiteName = 'Deakin University'
    $ADSyncFrequency = [TimeSpan]::FromHours(6)
    $AccountNotSyncedWarningAt = [TimeSpan]::FromSeconds($ADSyncFrequency.TotalSeconds * 2)
    $ADUsernameAttribute = 'Mail'
    $QuickActionGroupsChangedAfter = [DateTime]::Now.AddMinutes(5)
    $FullDiffAfter = [TimeSpan]::FromHours(1)
    $MaxAgentsToDeletePerRun = 10
    $MinAgents = 15
    # Assign language skills to new users (no validation post creation).
    #   Format is:
    #       @{
    #           'skill name' = proficiency
    #           'skill name2' = proficiency
    #       }
    $LanguageSkillsForNewAgents =
    @{
        'English - Spoken' = 2.5
    }

    CLEAR_QUEUED_SOC_MESSAGES

    Import-Module 'DeakinUtils'
    Import-Module 'DeakinPureCloudAPI'
    $PSDefaultParameterValues += Get-DknModuleDefaultParameterValues -ModuleName:'DeakinPureCloudAPI'

    $stateChanged = $false; $timeUsersFirstSeenMissing = [DateTime]::Now
    $state = Load-DknStateFile -DefaultState:@{ 'LastFullDiff' = [DateTime]::MinValue } -NoStateCreationSocWarning -WhatIf:$false
    if ( $state['StateIsNew'] ) { $stateChanged = $true }
    if ( $state.ContainsKey('SyncPendingUsers') -eq $false -or $state['SyncPendingUsers'] -isnot [Hashtable] )
    {
        WriteOutput ('SyncPendingUsers has been cleared') -Type:'Warning'
        $state['SyncPendingUsers'] = @{}
        $stateChanged = $true
        $timeUsersFirstSeenMissing = [DateTime]::MinValue
    }
    $syncPendingUsers = @{}

    ################################################################################
    #
    # Collect information
    #
    ################################################################################

    #WriteOutput ('Type="Info",Message="Getting AD group {0}"' -f $ComputerGroupName)
    #$adDomainName = (Get-WmiObject -Class:'Win32_ComputerSystem').Domain.ToLower()
    #$agentAdComputerGroup = @(Get-ADGroup -Identity:$ComputerGroupName -Property:@('DistinguishedName', 'WhenChanged'))
    #$agentAdComputers = @(  $agentAdComputerGroup | Get-ADGroupMember -Recursive | `
    #                        where objectClass -eq 'computer' | `
    #                        Get-ADComputer -Properties:'name', 'extensionAttribute7', 'extensionAttribute15' | `
    #                        where { $_.extensionAttribute7 -ne 'INACTIVE' -and [string]::IsNullOrEmpty($_.extensionAttribute15) -eq $false } | `
    #                        foreach { @{'Name' = 'DPN{0}{1}' -f $_.extensionAttribute15, $SoftphoneNameSuffix; 'HardwareId' = '{0}.{1}' -f $_.name, $adDomainName } }
    #                    )
    #$agentAdGroupRecentChange = @($agentAdComputerGroup | Where-Object WhenChanged -ge $QuickActionGroupsChangedAfter).Count -gt 0
    #$agentAdComputerGroup = $null
    #if ( $adDomainName -match '^du-dev' -and $agentAdComputers.Count -eq 0 )
    #{
    #    # There are no DU-DEV joined workstations, so fudge some data to test with.
    #    $agentAdComputers = @(@{'Name' = 'DPN41956' + $SoftphoneNameSuffix; 'HardwareId' = 'DPN{0}.{1}' -f '41956', $adDomainName })
    #}
    #WriteOutput ('Type="Info",ADGroupName="{0}",MemberCount="{1}"' -f $ComputerGroupName, $agentAdComputers.Count)

    #
    # Get all PureCloud AD groups
    #
    WriteOutput ('Type="Info",Message="Getting AD groups like {0}"' -f $AccessGroupsAreLike)
    $adGroups = @(Get-ADGroup -Filter:{ Name -like $AccessGroupsAreLike } -Property:@('Name', 'Description', 'WhenChanged'))
    $shouldQuickAction = @($adGroups | Where-Object WhenChanged -ge $QuickActionGroupsChangedAfter).Count -gt 0 #-or $agentAdGroupRecentChange
    $shouldFullDiff = [DateTime]::Now.Subtract($state['LastFullDiff']) -gt $FullDiffAfter -or $FullDiff

    WriteOutput ('Type="Info",ShouldQuickAction="{0}",ShouldFullDiff="{1}",ADGroupCount="{2}"' -f $shouldQuickAction, $shouldFullDiff, $adGroups.Count)
    if ( ($shouldFullDiff -or $shouldQuickAction) -eq $false ) { return }

    $didNumbers =
    @(
        Invoke-PureCloudWebRequest '/api/v2/architect/ivrs' |
            Select-Object -ExpandProperty entities |
            Select-Object -ExpandProperty:'dnis'
    )

    $adGroupMembers = @{}
    $adGroupByQueueId = @{}
    $adUsers = @{}
    $adUsersByDn = @{}
    $adAttrs = @(
        $ADUsernameAttribute
        'cn'
        'department'
        'DisplayName'
        'DistinguishedName'
        'Enabled'
        'Mail'
        'manager'
        'msExchUmEnabledFlags'
        'msRTCSIP-Line'
        'title'
    )
    $adAttrs = @($adAttrs | Select-Object -Unique)
    $noUsernames = @{}
    foreach ( $group in $adGroups )
    {
        $members = @(Get-ADGroupMember -Identity:$group.DistinguishedName -Recursive | Get-ADUser -Properties:$adAttrs) # | Where-Object Enabled -eq $true)
        foreach ( $member in $members )
        {
            if ( [string]::IsNullOrEmpty($member.$ADUsernameAttribute) )
            {
                if ( $noUsernames.ContainsKey($member.cn) -eq $false )
                {
                    QUEUE_SOC_MESSAGE -Level:1 -Message:('User {0} does not have the {1} attribute set' -f $member.cn, $ADUsernameAttribute)
                }
                $noUsernames[$member.cn] = $true
            }
            else
            {
                $adUsers[$member.$ADUsernameAttribute] = $member
                $adUsersByDn[$member.'DistinguishedName'] = $member
            }
        }
        $members = @($members | Where-Object { [string]::IsNullOrEmpty($_.$ADUsernameAttribute) -eq $false })
        $adGroupMembers[$group.Name] = $members
        if ( $null -ne $group.Description -and $group.Description.Trim() -match '^[0-9a-f\-]{36}$' )
        {
            $queueId = $matches[0]
            if ( $adGroupByQueueId.ContainsKey($queueId) )
            {
                QUEUE_SOC_MESSAGE -Level:1 -Message:('Queue ID {0} is set on multiple IAM groups "{1}" and "{2}"' -f $queueId, $group.Name, $adGroupByQueueId[$queueId])
                continue
            }
            $adGroupByQueueId[$queueId] = $group.Name
            WriteOutput ('Type="Info",ADGroupName="{0}",MemberCount="{1}",ADQueueId="{2}"' -f $group.Name, $members.Count, $queueId)
        }
        else
        {
            WriteOutput ('Type="Info",ADGroupName="{0}",MemberCount="{1}"' -f $group.Name, $members.Count)
        }
    }
    $adGroups = @($adGroupMembers.psbase.Keys)

    #
    # Get PureCloud users
    #
    WriteOutput ('Type="Info",Message="Getting PureCloud users"')
    $res = Invoke-PureCloudWebRequest -Call:'/api/v2/users/' -Method:'GET' -WhatIf:$false -Confirm:$false
    $usersById = @{}
    $usersByEmail = @{}
    $usersByExtension = @{}
    $extensionForUser = @{}
    foreach ( $user in @($res | Select-Object -ExpandProperty:'entities') )
    {
        $usersById[$user.id] = $user
        $username = Get-DknPropertyValue -InputObject:$user -Path:'username'
        $name = Get-DknPropertyValue -InputObject:$user -Path:'name'
        if ( [string]::IsNullOrEmpty($username) -eq $false )
        {
            $usersByEmail[$username] = $user
            if ( $adUsers.ContainsKey($username) -and $adUsers[$username].'msRTCSIP-Line' -match ';ext=(\d+)$' )
            {
                $ext = $matches[1]
                $usersByExtension[$ext] = $user
                $extensionForUser[$username] = $ext
            }
        }
        else
        {
            QUEUE_SOC_MESSAGE -Level:1 -Message:('PureCloud user "{0}" ("{1}") does not have an email address' -f $user.id, $name)
        }
    }

    #
    # Diff/create users
    #
    # HACK: The key used is email address. This is clearly bad, but there isn't another suitable field to put the ACCOUNT_ID into, and it is what PureCloud uses as the unique identifier.
    $skipAgentDeletion = $false
    $missing    = @($adUsers.psbase.Keys      | Where-Object { $_ -notin $usersByEmail.psbase.Keys })
    # Never, ever delete its-systems or sd-leadership just in case the loader goes crazy and deletes everyone. These accounts also have local passwords in PureCloud to get around ADFS issues.
    $extra      = @($usersByEmail.psbase.Keys | Where-Object { $_ -match '@(dev\.)?deakin\.edu\.au$' -and $_ -notmatch '^(its-systems|sd-leadership)@' -and $_ -notin $adUsers.psbase.Keys })
    if ( $adUsers.psbase.Keys.Count -lt $MinAgents )
    {
        $skipAgentDeletion = $true
        QUEUE_SOC_MESSAGE -Level:1 -Message:('Too few agents: {0}, expected at least {1}. Agent provisioning disabled.' -f $adUsers.psbase.Keys.Count, $MinAgents)
    }
    if ( $extra.Count -gt $MaxAgentsToDeletePerRun )
    {
        if ( $Force -ne $true )
        {
            $skipAgentDeletion = $true
            QUEUE_SOC_MESSAGE -Level:1 -Message:('Too many agents ({0}) due to be deleted, see log/output for more information. Agent provisioning disabled.' -f $extra.Count)
        }
        WriteOutput ('Agents due to be deleted:') -Type:'Warning'
        foreach ( $agent in $extra )
        {
            WriteOutput ('    {0}' -f $agent) -Type:'Warning'
        }
    }

    #
    # Fix attributes
    #
    foreach ( $email in $adUsers.psbase.Keys )
    {
        $adUser = $adUsers[$email]
        if ( $usersByEmail.ContainsKey($email) -eq $false ) { continue }
        $pcUser = $usersByEmail[$email]
        $username = Get-DknPropertyValue -InputObject:$pcUser -Path:'username'

        $body = @{}

        $attrName = 'name'
        $adValue = $adUser.DisplayName
        $pcValue = Get-DknPropertyValue -InputObject:$pcUser -Path:$attrName
        if ( $adValue -cne $pcValue -and [string]::IsNullOrEmpty($adValue) -eq $false )
        {
            WriteOutput ('Type="AgentUpdate",Id="{0}",Username="{1}",Attribute="{2}",OldValue="{3}",NewValue="{4}"' -f $pcUser.Id, $username, $attrName, $pcValue, $adValue)
            $body[$attrName] = $adValue
        }

        $attrName = 'department'
        $adValue = $adUser.$attrName
        $pcValue = Get-DknPropertyValue -InputObject:$pcUser -Path:$attrName
        if ( $adValue -cne $pcValue -and [string]::IsNullOrEmpty($adValue) -eq $false )
        {
            WriteOutput ('Type="AgentUpdate",Id="{0}",Username="{1}",Attribute="{2}",OldValue="{3}",NewValue="{4}"' -f $pcUser.Id, $username, $attrName, $pcValue, $adValue)
            $body[$attrName] = $adValue
        }

        $attrName = 'title'
        $adValue = $adUser.$attrName
        $pcValue = Get-DknPropertyValue -InputObject:$pcUser -Path:$attrName
        if ( $adValue -cne $pcValue -and [string]::IsNullOrEmpty($adValue) -eq $false )
        {
            WriteOutput ('Type="AgentUpdate",Id="{0}",Username="{1}",Attribute="{2}",OldValue="{3}",NewValue="{4}"' -f $pcUser.Id, $username, $attrName, $pcValue, $adValue)
            $body[$attrName] = $adValue
        }

        $attrName = 'manager'
        $adValue = ''
        if ( $null -ne $adUser.$attrName -and $adUsersByDn.ContainsKey($adUser.$attrName) -and $adUser.$attrName -ne $adUser.DistinguishedName )
        {
            $adManager = $adUsersByDn[$adUser.$attrName].$ADUsernameAttribute
            if ( $usersByEmail.ContainsKey($adManager) )
            {
                $adValue = $usersByEmail[$adManager].id
            }
        }
        $pcValue = Get-DknPropertyValue -InputObject:$pcUser -Path:'manager.id'
        if ( [string]::IsNullOrEmpty($adValue) ) { $adValue = '' }
        if ( [string]::IsNullOrEmpty($pcValue) ) { $pcValue = '' }
        if ( $adValue -ne $pcValue )
        {
            WriteOutput ('Type="AgentUpdate",Id="{0}",Username="{1}",Attribute="{2}",OldValue="{3}",NewValue="{4}"' -f $pcUser.Id, $username, $attrName, $pcValue, $adValue)
            $body[$attrName] = $adValue
        }

        # $conflictingNumber = $false
        $addresses = @(Get-DknPropertyValue -InputObject:$pcUser -Path:'primaryContactInfo.address')
        $addresses += @(Get-DknPropertyValue -InputObject:$pcUser -Path:'addresses.address')
        $brokenUsers = @()
        foreach ( $address in $addresses )
        {
            if ($address -match '^\+\d+' -and $brokenUsers -notcontains $email) 
            {
                QUEUE_SOC_MESSAGE -Level:1 -Message:(
                    'User "{0}" has a phone number ({1}) in PureCloud, please remove it.' -f
                        $email,
                        $address
                )
                $brokenUsers += $email
            }
            if ( $address -in $didNumbers )
            {
                QUEUE_SOC_MESSAGE -Level:2 -Message:(
                    'User "{0}" has a phone number ({1}) in PureCloud, please remove it, this can break an entire call centre' -f
                        $email,
                        $address
                )
            }
        }

        # $attrName = 'primaryContactInfo'
        # $pcValue = Get-DknPropertyValue -InputObject:$pcUser -Path:$attrName
        # $adValue = @($pcValue | Where-Object mediaType -ne 'PHONE')
        # if ( $adValue -cne $pcValue )
        # {
        #     WriteOutput ('Type="AgentUpdate",Id="{0}",Attribute="{1}",OldValue="{2}",NewValue="{3}"' -f $pcUser.Id, $attrName, $pcValue, $adValue)
        #     $body[$attrName] = $adValue
        # }

        # $attrName = 'addresses'
        # $pcValue = Get-DknPropertyValue -InputObject:$pcUser -Path:$attrName
        # $adValue = @($pcValue | Where-Object mediaType -ne 'PHONE')
        # if ( $adValue -cne $pcValue )
        # {
        #     WriteOutput ('Type="AgentUpdate",Id="{0}",Attribute="{1}",OldValue="{2}",NewValue="{3}"' -f $pcUser.Id, $attrName, $pcValue, $adValue)
        #     $body[$attrName] = $adValue
        # }

        if ( $body.psbase.Keys.Count )
        {
            $body['version'] = $pcUser.version
            $res = Invoke-PureCloudWebRequest -Call:('/api/v2/users/{0}' -f $pcUser.id) -Method:'PATCH' -Body:$body
        }
    }

    #
    # Delete extra agents
    #
    if ( $skipAgentDeletion -eq $false )
    {
        foreach ( $email in $extra )
        {
            if ( $usersByEmail.ContainsKey($email) -eq $false ) { continue }
            $pcUser = $usersByEmail[$email]
            $pcId = $pcUser.id
            WriteOutput ('Type="AgentDelete",Id="{0}",Email="{1}"' -f $pcId, $email)
            $res = Invoke-PureCloudWebRequest -Call:('/api/v2/users/{0}' -f $pcId) -Method:'DELETE'
        }
    }

    #
    # Create missing agents
    #
    if ( $missing.Count -gt 0 )
    {
        # $allDeletedUsers = $null

        # Get language skills for later use.
        $langSkillById = @{}
        $langSkillByName = @{}
        Invoke-PureCloudWebRequest -Call:'/api/v2/routing/languages' -Method:'GET' |
            Select-Object -ExpandProperty:'entities' |
            ForEach-Object {
                $langSkillById[$_.id] = $_
                $langSkillByName[$_.name] = $_
            }

        :nextUser foreach ( $email in $missing )
        {
            $exceptionRecovered = $false
            $adUser = $adUsers[$email]
            try
            {
                WriteOutput ('Type="AgentCreate",Email="{0}",Name="{1}"' -f $email, $adUser.DisplayName)
                $res = Invoke-PureCloudWebRequest -Call:'/api/v2/users' -Method:'POST' -Body:@{
                   'name' = $adUser.DisplayName
                   'department' = $adUser.department
                   'email' = $email
                   'title' = $adUser.title
                    # ConvertTo-Json does not handle '\' in strings (on PSv4 at least)
                   'password' = (Get-DknRandomPassword) -replace '\\'
                }
            }
            catch [Net.WebException], [InvalidOperationException]
            {
                $isDuplicateEmail = (
                    $_.Exception -is [Net.WebException] -and
                    $_.Exception.Status -eq [Net.WebExceptionStatus]::ProtocolError -and
                    $response.StatusCode.value__ -eq 409
                ) -or (
                    $_.Exception -is [InvalidOperationException] -and
                    $_.Exception.Message -match 'duplicate value for.+mail'
                )

                if ( $isDuplicateEmail )
                {
                    # HTTP 409: Conflict (or 400 now it seems)
                    # The email address is the username in PureCloud and must be unique.
                    # Users cannot be purged, only flagged as deleted.
                    # There isn't a suitable field that we can get to via the API to populate with ACCOUNT_ID or username
                    # We can't do decent searches over the users marked as deleted
                    # This all adds up to bad. On creation if we get a 409 it probably means the email address is on a deleted user
                    # We should restore the user, probably, unless of course the email address was reused. But how do we know?
                    # Even if it is reuse, what do we do with the deleted account? We could just change the email to <NAME_EMAIL>
                    # I'm going down the path of allowing reuse, if this becomes a problem, we will need to revisit. This only impacts contact centre agents of which
                    # there are only a small number currently. If we end up bring a lot more users in, this will need to be addressed.
                    $params =
                    @{
                        'Method' = 'Post'
                        'Call' = 'https://apps.mypurecloud.com.au/platform/api/v2/search'
                        'Body' =
                        @{
                            'types' = @('users')
                            'query' =
                            @(
                                @{
                                    'type' = 'EXACT'
                                    'fields' = @('state')
                                    'value' = 'deleted'
                                }
                                @{
                                    'type' = 'EXACT'
                                    'fields' = @('email', 'username')
                                    'value' = $email
                                }
                            )
                        }
                        'WhatIf' = $false
                    }
                    $foundDeleted = @(Invoke-PureCloudWebRequest @params | Select-Object -ExpandProperty:'results')
                    if ( $foundDeleted.Count -ne 1 )
                    {
                        QUEUE_SOC_MESSAGE -Level:1 -Message:('User with email address "{0}" cannot be created because of a conflict, manually search deleted users and restore' -f $email)
                        continue nextUser

                    }
                    $params =
                    @{
                        'Call' = ('/api/v2/users/' + $foundDeleted[0].guid + '?state=deleted')
                        'WhatIf' = $false
                    }
                    $deletedUser = Invoke-PureCloudWebRequest @params
                    $res = $null
                    try
                    {
                        $params =
                        @{
                            'Call' = '/api/v2/users/{0}' -f $deletedUser.id
                            'Method' = 'PATCH'
                            'Body' =
                            @{
                                'state' = 'active'
                                'version' = $deletedUser.version
                            }
                        }
                        $res = Invoke-PureCloudWebRequest @params
                        Start-Sleep -Seconds:15
                    }
                    catch [Net.WebException]
                    {
                        $httpCode = $null
                        if ( $_.Exception.Status -eq [Net.WebExceptionStatus]::ProtocolError -and $null -ne $_.Exception.Response ) { $httpCode = $_.Exception.Response.StatusCode.value__ }
                    }
                    if ( $null -eq $res )
                    {
                        QUEUE_SOC_MESSAGE -Level:1 -Message:('Failed to reinstate deleted account with email address "{0}"' -f $email)
                        continue nextUser
                    }
                    $exceptionRecovered = $true
                }
                if ( -not $exceptionRecovered )
                {
                    throw
                }
            }
            # Assign any required language skills to the new user
            # Language skills are only assigned on creation to allow control of
            # channels on a per user basis.

            # Find the new user
            $body = @{
                'types' = @('users')
                'query' = @(
                    @{
                        'type' = 'TERM'
                        'fields' = @('email')
                        'value' = $email
                        'operator' = 'AND'
                    }
                )
            }
            $pcUser = Invoke-PureCloudWebRequest -Call:'/api/v2/search?profile=false' -Method:'POST' -Body:$body |
                Select-Object -ExpandProperty:'results'
            if ( $null -eq $pcUser )
            {
                QUEUE_SOC_MESSAGE -Level:1 -Message:('Failed to set skills on new user {0} (user missing)' -f $email)
                continue nextUser
            }

            # Check the current skills
            $existingLangSkills = @(
                Invoke-PureCloudWebRequest -Call:('/api/v2/users/{0}/routinglanguages' -f $pcUser.id) -Method:'GET' |
                    Select-Object -ExpandProperty:'entities' |
                    Select-Object -ExpandProperty:'name'
            )
            
            # Add the skill if needed
            foreach ( $langSkill in $LanguageSkillsForNewAgents.psbase.Keys )
            {
                if ( $langSkill -in $existingLangSkills )
                {
                    continue
                }
                if ( $langSkill -NotIn $langSkillByName.psbase.Keys )
                {
                    QUEUE_SOC_MESSAGE -Level:1 -Message:(
                        'Language skill "{0}" does not exist in the environment' -f
                            $langSkill
                    )
                }
                $langProf = $LanguageSkillsForNewAgents[$langSkill]
                $langId = $langSkillByName[$langSkill].id
                WriteOutput (
                    'Type="AgentAssignLangSkill",Email="{0}",Name="{1}",LangSkill="{2}",LangProficiency="{3}"' -f
                        $email,
                        $adUser.DisplayName,
                        $langSkill,
                        $langProf
                )
                $params = @{
                    Call = '/api/v2/users/{0}/routinglanguages' -f $pcUser.id
                    Method = 'POST'
                    Body = @{
                        'id' = $langId
                        'proficiency' = $langProf
                    }
                }
                Invoke-PureCloudWebRequest @params
            }
        }
    }

    #
    # Get PureCloud role group membership matching AD groups
    #
    WriteOutput ('Type="Info",Message="Getting PureCloud role groups"')
    $res = Invoke-PureCloudWebRequest -Call:'/api/v2/authorization/roles?userCount=false' -Method:'GET' -WhatIf:$false -Confirm:$false
    $roles = @{}
    $roleMembers = @{}
    $adGroupsInUse = @{}
    foreach ( $role in @($res | Select-Object -ExpandProperty:'entities') )
    {
        $name = $role.name
        $isEmployeeRole = $name -eq 'employee'
        if ( $name -notmatch '^wg-' -and $isEmployeeRole -eq $false ) { continue }
        if ( $adGroupMembers.ContainsKey($name) -eq $false -and $isEmployeeRole -eq $false )
        {
            WriteOutput ('Type="Warning",RoleName="{0}",ADGroupName="NULL",Message="No matching IAM group"' -f $name)
            QUEUE_SOC_MESSAGE -Level:1 -Message:('IAM group for role group {0} is missing' -f $name)
            continue
        }
        $roles[$name] = $role
        $id = $role.id
        $res = Invoke-PureCloudWebRequest -Call:('/api/v2/authorization/roles/{0}/users' -f $id) -Method:'GET' -WhatIf:$false -Confirm:$false
        $members = @($res | Select-Object -ExpandProperty:'entities' | Select-Object -ExpandProperty:'id')
        $roleMembers[$name] = $members
        if ( $isEmployeeRole )
        {
            $adMemberCount = $usersById.psbase.Keys.Count
        }
        else
        {
            $adMemberCount = $adGroupMembers[$name].Count
            $adGroupsInUse[$name] = $true
        }
        WriteOutput ('Type="Info",RoleName="{0}",ADGroupName="{1}",RoleMemberCount="{2}",ADMemberCount="{3}"' -f $name, $name, $members.Count, $adMemberCount)
    }

    #
    # Check users have been replicated out
    #

    # Quickly check what queues we have so we know what AD groups are active.
    $res = Invoke-PureCloudWebRequest -Call:'/api/v2/routing/queues' -Method:'GET' -WhatIf:$false -Confirm:$false
    foreach ( $queue in @($res | Select-Object -ExpandProperty:'entities') )
    {
        $name = $queue.name
        $id = $queue.id
        if ( $adGroupByQueueId.ContainsKey($id) -eq $false ) { continue }
        $adGroup = $adGroupByQueueId[$id]
        $adGroupsInUse[$adGroup] = $true
    }

    # OK, now check AD users have synced.
    $missingUsers = @{}
    $members = @()
    foreach ( $adGroup in $adGroupsInUse.psbase.Keys )
    {
        $members += $adGroupMembers[$adGroup]
    }
    $members = @($members | Select-Object -Unique)
    foreach ( $member in $members )
    {
        $username = $member.$ADUsernameAttribute
        if ( [string]::IsNullOrEmpty($username) )
        {
            $msg = ('User {0} does not have the {1} attribute set' -f $member.Name, $ADUsernameAttribute)
            QUEUE_SOC_MESSAGE -Level:1 -Message:$msg
        }
        elseif ( $usersByEmail.ContainsKey($username) -eq $false )
        {
            $whenFirstSeenPending = $timeUsersFirstSeenMissing
            if ( $state['SyncPendingUsers'].ContainsKey($username) )
            {
                $whenFirstSeenPending = $state['SyncPendingUsers'][$username]
            }
            else
            {
                $state['SyncPendingUsers'][$username] = $timeUsersFirstSeenMissing
                $stateChanged = $true
            }
            $syncPendingUsers[$username] = $whenFirstSeenPending
            $timeSeenPending = [DateTime]::Now.Subtract($whenFirstSeenPending)
            $level = 0
            $msg = ('User {0} ({1}) has not been synced to PureCloud, first seen at {2} ({3} ago)' -f $username, $member.Name, $whenFirstSeenPending.ToString('s'), (Format-DknTimeSpan -TimeSpan:$timeSeenPending))
            if ( $timeSeenPending -gt $AccountNotSyncedWarningAt ) { $level++ }
            QUEUE_SOC_MESSAGE -Level:$level -Message:$msg
            $missingUsers[$username] = $true
        }
    }
    $members = $null

    #
    # Get PureCloud queue membership matching AD groups
    #
    WriteOutput ('Type="Info",Message="Getting PureCloud queues"')
    $queues = @{}
    $queueMembers = @{}
    $queueAdGroup = @{}
    $userQueueMembership = @{}
    $usersThatShouldHavePhones = @{}
    foreach ( $queue in @($res | Select-Object -ExpandProperty:'entities') )
    {
        $name = $queue.name
        $id = $queue.id
        if ( $adGroupByQueueId.ContainsKey($id) -eq $false )
        {
            WriteOutput ('Type="Warning",QueueName="{0}",QueueID="{1}",Message="No matching AD group"' -f $name, $id)
            QUEUE_SOC_MESSAGE -Level:1 -Message:('Queue "{0}" has no IAM group (ID {1})' -f $name, $id)
            continue
        }
        $adGroup = $adGroupByQueueId[$id]
        $queueAdGroup[$name] = $adGroup
        $queues[$name] = $queue
        $res = Invoke-PureCloudWebRequest -Call:('/api/v2/routing/queues/{0}/users' -f $id) -Method:'GET' -WhatIf:$false -Confirm:$false
        $members = @($res | Select-Object -ExpandProperty:'entities' | Select-Object -ExpandProperty:'id')
        $queueMembers[$name] = $members
        $adMembers = $adGroupMembers[$adGroup]
        foreach ( $member in $adMembers )
        {
            $username = $member.$ADUsernameAttribute
            if ( [string]::IsNullOrEmpty($username) -or $missingUsers.ContainsKey($username) ) { continue }
            if ( $usersByEmail.ContainsKey($username) -eq $false )
            {
                QUEUE_SOC_MESSAGE -Level:1 -Message:('Unable to find user {0} ({1}) in PureCloud' -f $username, $member.Name)
                continue
            }
            $userid = $usersByEmail[$username].id
            $usersThatShouldHavePhones[$userid] = $true

            if ( $userQueueMembership.ContainsKey($userid) -eq $false )
            {
                $userQueueMembership[$userid] = @()
            }
            $userQueueMembership[$userid] += $id
        }
        WriteOutput ('Type="Info",QueueName="{0}",ADGroupName="{1}",QueueID="{2}",QueueMemberCount="{3}",ADMemberCount="{4}"' -f $name, $adGroup, $id, $members.Count, $adMembers.Count)
    }

    #
    # Get PureCloud Directory Groups with names matching wg-* (should match AD queue group names)
    #
    # Loader is only authoritative over official groups named 'wg-*'
    $res = @(
        Invoke-PureCloudWebRequest -Call:'/api/v2/groups' -Method:'GET' -WhatIf:$false -Confirm:$false |
            Select-Object -ExpandProperty:'entities' |
            Where-Object { $_.name -match '^wg-' -and $_.type -eq 'official' }
    )
    $pcDirGroupsByName = @{}
    $pcDirGroupMembersByName = @{}
    :nextGroup foreach ( $group in $res )
    {
        $pcDirGroupsByName[$group.name] = $group
        $pcDirGroupMembersByName[$group.name] = @()
        $groupId = $group.id
        $members = Invoke-PureCloudWebRequest -Call:"/api/v2/groups/${groupId}/members?pageSize=500" -Method:'GET' -WhatIf:$false -Confirm:$false
        :nextMember foreach ( $member in $members.entities )
        {
            $member = $member.id
            $pcDirGroupMembersByName[$group.name] += $member
        }
        WriteOutput ('Type="Info",DirGroupName="{0}",MemberCount="{1}",DirGroupId="{2}"' -f $group.name, $pcDirGroupMembersByName[$group.name].Count, $groupId)
    }

    #
    # Get PureCloud phones
    #
    WriteOutput ('Type="Info",Message="Getting PureCloud phones"')
    $res = Invoke-PureCloudWebRequest -Call:'/api/v2/telephony/providers/edges/sites' -Method:'GET' -WhatIf:$false -Confirm:$false
    $sites = @($res | Select-Object -ExpandProperty:'entities' | Where-Object name -eq $SiteName)
    if ( $sites.Count -ne 1 )
    {
        $res | Out-Host
        throw ('Found {0} sites with name "{1}"' -f $sites.Count, $SiteName)
    }
    $site = $sites[0]
    $res = Invoke-PureCloudWebRequest -Call:'/api/v2/telephony/providers/edges/phonebasesettings' -Method:'GET' -WhatIf:$false -Confirm:$false
    $phoneBaseSettingsWebRtc = @($res | Select-Object -ExpandProperty:'entities' | Where-Object name -eq $WebRtcPhoneBaseSettingsName)
    #$phoneBaseSettingsSoftphone = @($res | Select-Object -ExpandProperty:'entities' | Where-Object name -eq $SoftphoneBaseSettingsName)
    #$phoneBaseSettingsRemotePhone = @($res | Select-Object -ExpandProperty:'entities' | Where-Object name -eq $RemotePhoneBaseSettingsName)
    if ( $phoneBaseSettingsWebRtc.Count -ne 1 )
    {
        $res | Out-Host
        throw ('Found {0} WebRTC phone base settings with name "{1}"' -f $phoneBaseSettingsWebRtc.Count, $WebRtcPhoneBaseSettingsName)
    }
    #if ( $phoneBaseSettingsSoftphone.Count -ne 1 )
    #{
    #    $res | Out-Host
    #    throw ('Found {0} PureCloud Softphone base settings with name "{1}"' -f $phoneBaseSettingsSoftphone.Count, $SoftphoneBaseSettingsName)
    #}
    #if ( $phoneBaseSettingsRemotePhone.Count -ne 1 )
    #{
    #    $res | Out-Host
    #    throw ('Found {0} PureCloud remote phone base settings with name "{1}"' -f $phoneBaseSettingsRemotePhone.Count, $RemotePhoneBaseSettingsName)
    #}
    $phoneBaseSettingsWebRtc = $phoneBaseSettingsWebRtc[0]
    #$phoneBaseSettingsSoftphone = $phoneBaseSettingsSoftphone[0]
    #$phoneBaseSettingsRemotePhone = $phoneBaseSettingsRemotePhone[0]

    #
    # PureCloud WebRTC phones
    #
    $res = Invoke-PureCloudWebRequest -Call:('/api/v2/telephony/providers/edges/phones/template?phoneBaseSettingsId={0}' -f $phoneBaseSettingsWebRtc.id) -Method:'GET' -WhatIf:$false -Confirm:$false
    $lineBaseSettingsIdWebRtc = $res.lineBaseSettings.id
    $res = @(Invoke-PureCloudWebRequest -Call:'/api/v2/telephony/providers/edges/phones' -Method:'GET' -WhatIf:$false -Confirm:$false -Body:@{
                    'phoneBaseSettings.id'  = $phoneBaseSettingsWebRtc.id
                    'fields'                = 'webRtcUser'
            } | Select-Object -ExpandProperty:'entities')
    $phonesWebRtcById = @{}
    $phonesWebRtcByUser = @{}
    foreach ( $phone in $res )
    {
        # This loader owns any phone based off the base settings for the type
        #if ( $phone.name -notmatch ([Regex]::Escape($WebRtcNameSuffix) + '$') ) { continue }
        $phonesWebRtcById[$phone.id] = $phone
        if ( (Test-Member -InputObject:$phone -PropertyName:'webRtcUser') -and $null -ne $phone.webRtcUser -and $null -ne $phone.webRtcUser.id )
        {
            $phonesWebRtcByUser[$phone.webRtcUser.id] = $phone
        }
    }
    WriteOutput ('Type="Info",WebRtcPhoneCount="{0}",QueueUserCount="{1}"' -f $phonesWebRtcById.psbase.Keys.Count, $usersThatShouldHavePhones.psbase.Keys.Count)

    #
    # Skill data
    #
    $queueNames = @($queues.psbase.Keys)
    $queueSkills = @{}
    $skillsById = @{}
    $skillsByName = @{}
    $userSkills = @{}
    $res = @(
        Invoke-PureCloudWebRequest -Call:'/api/v2/routing/skills' -WhatIf:$false -Confirm:$false | `
        Select-Object -ExpandProperty:'entities'
    )
    WriteOutput ('Type="Info",SkillCount="{0}"' -f $res.Count)
    foreach ( $skill in $res )
    {
        $skillsById[$skill.id] = $skill
        $skillsByName[$skill.name] = $skill
    }

    # Get all skills for each queue.
    foreach ( $queueName in $queueNames )
    {
        # Make sure there are no queue name collisions
        $queueCollisions = @($queueNames | Where-Object { $_.StartsWith($queueName) })
        if ( $queueCollisions.Count -gt 1 )
        {
            throw (
                'Queue name collision: {0}' -f `
                [string]::Join(', ', $queueCollisions)
            )
        }

        $skills = @(
            $skillsByName.psbase.Keys | `
                Where-Object { $_.StartsWith($queueName) }
        )

        $queueId = $queues[$queueName].id
        $queueSkills[$queueId] = $skills
        $typeLevel = if ( $skills.Count -eq 0 ) { 'Warning' } else { 'Info' }
        WriteOutput (
            'Type="{0}",QueueName="{1}",SkillCount="{2}",Skills="{3}",QueueId="{4}"' -f `
                $typeLevel, `
                $queueName, `
                $skills.Count, `
                [string]::Join(', ', $skills), `
                $queueId
        )

        if ( $skills.Count -eq 0 )
        {
            # $msg = 'Queue "{0}" has no matching skills' -f $queueName
            # QUEUE_SOC_MESSAGE -Level:'Warning' -Message:$msg
            continue
        }

        $queue = $queues[$queueName]
        $adGroupName = $queueAdGroup[$queueName]
        $actualMembers = $queueMembers[$queueName]
        foreach ( $member in $adGroupMembers[$adGroupName] )
        {
            $username = $member.$ADUsernameAttribute
            if ( [string]::IsNullOrEmpty($username) -or $missingUsers.ContainsKey($username) ) { continue }
            if ( $usersByEmail.ContainsKey($username) -eq $false )
            {
                continue
            }

            $userId = $usersByEmail[$username].id

            if ( $userSkills.ContainsKey($userId) )
            {
                continue
            }

            $currentSkills = @(
                Invoke-PureCloudWebRequest -Call:"/api/v2/users/${userId}/routingskills" -WhatIf:$false -Confirm:$false | `
                    Select-Object -ExpandProperty:'entities' | `
                    Select-Object -ExpandProperty:'name'
            )
            $userSkills[$userId] = $currentSkills
        }
    }

    #
    # PureCloud Softphones
    #
    #WriteOutput ('Type="Info",Message="Getting PureCloud Softphones"')
    #$res = Invoke-PureCloudWebRequest -Call:('/api/v2/telephony/providers/edges/phones/template?phoneBaseSettingsId={0}' -f $phoneBaseSettingsSoftphone.id) -Method:'GET' -WhatIf:$false -Confirm:$false
    #$lineBaseSettingsIdSoftphone = $res.lineBaseSettings.id
    #$res = @(Invoke-PureCloudWebRequest -Call:'/api/v2/telephony/providers/edges/phones' -Method:'GET' -WhatIf:$false -Confirm:$false -Body:@{
    #                'phoneBaseSettings.id'  = $phoneBaseSettingsSoftphone.id
    #                'expand'                = 'status'
    #                'fields'                = 'lines.loggedInUser'
    #        } | Select-Object -ExpandProperty:'entities')
    #$phonesSoftById = @{}
    #foreach ( $phone in $res )
    #{
    #    # This loader owns any phone based off the base settings for the type
    #    #if ( $phone.name -notmatch ([Regex]::Escape($SoftphoneNameSuffix) + '$') ) { continue }
    #    $phonesSoftById[$phone.id] = $phone
    #}
    #WriteOutput ('Type="Info",SoftphoneCount="{0}",AgentComputerCount="{1}"' -f $phonesSoftById.psbase.Keys.Count, $agentAdComputers.Count)

    #
    # PureCloud Remote Phones (Deakin Skype)
    #
    #WriteOutput ('Type="Info",Message="Getting PureCloud Skype Remote Phones"')
    #$res = Invoke-PureCloudWebRequest -Call:('/api/v2/telephony/providers/edges/phones/template?phoneBaseSettingsId={0}' -f $phoneBaseSettingsRemotePhone.id) -Method:'GET' -WhatIf:$false -Confirm:$false
    #$lineBaseSettingsIdRemotePhone = $res.lineBaseSettings.id
    #$res = @(Invoke-PureCloudWebRequest -Call:'/api/v2/telephony/providers/edges/phones' -Method:'GET' -WhatIf:$false -Confirm:$false -Body:@{
    #                'phoneBaseSettings.id'  = $phoneBaseSettingsRemotePhone.id
    #                'expand'                = 'lines'
    #        } | Select-Object -ExpandProperty:'entities')
    #$phonesRemoteById = @{}
    #$phonesRemoteByUser = @{}
    #foreach ( $phone in $res )
    #{
    #    # This loader owns any phone based off the base settings for the type
    #    #if ( $phone.name -notmatch ([Regex]::Escape($RemotePhoneNameSuffix) + '$') ) { continue }
    #    $phonesRemoteById[$phone.id] = $phone

    #    # Do number mapping.
    #    $addrs = @(Get-DknPropertyValue -InputObject:$phone -Path:'lines.properties.station_remote_address.value.instance')
    #    if ( $addrs.Count -eq 1 -and $usersByExtension.ContainsKey($addrs[0]) )
    #    {
    #        $userid = $usersByExtension[$addrs[0]].id
    #        $phonesRemoteByUser[$userid] = $phone
    #    }
    #}
    #WriteOutput ('Type="Info",RemotePhoneCount="{0}",QueueUserCount="{1}"' -f $phonesRemoteById.psbase.Keys.Count, $usersThatShouldHavePhones.psbase.Keys.Count)

    ################################################################################
    #
    # Diff and queue actions
    #
    ################################################################################

    if ( $WhatIfPreference )
    {
        Write-Warning ('Running in WhatIf mode, no changes will be made')
    }

    #
    # Role membership
    #
    $apiChanges = @()
    foreach ( $roleName in $roles.psbase.Keys )
    {
        $role = $roles[$roleName]
        $isEmployeeRole = $roleName -eq 'employee'
        $actualMembers = $roleMembers[$roleName]
        if ( $isEmployeeRole )
        {
            $expectedMembers = @($usersById.psbase.Values | ForEach-Object { $_.id })
        }
        else
        {
            $expectedMembers = @()
            foreach ( $member in $adGroupMembers[$roleName] )
            {
                $username = $member.$ADUsernameAttribute
                if ( [string]::IsNullOrEmpty($username) -or $missingUsers.ContainsKey($username) ) { continue }
                if ( $usersByEmail.ContainsKey($username) )
                {
                    $expectedMembers += $usersByEmail[$username].id
                }
                else
                {
                    QUEUE_SOC_MESSAGE -Level:1 -Message:('Unable to find user {0} ({1}) in PureCloud' -f $username, $member.Name)
                }
            }
        }
        if ( $expectedMembers.Count -eq 0 )
        {
            throw ('Role {0} has no members, aborting script' -f $roleName)
        }
        $missing    = @($expectedMembers    | Where-Object { $_ -notin $actualMembers      })
        $extra      = @($actualMembers      | Where-Object { $_ -notin $expectedMembers    })
        if ( $isEmployeeRole ) { $extra = @() }
        if ( $missing.Count -gt 0 )
        {
            $logEntries = @()
            foreach ( $id in $missing )
            {
                $member = $usersById[$id]
                $logEntries += ('Type="RoleMemberAdd",RoleName="{0}",Username="{1}",RoleId="{2}",UserId="{3}"' -f $roleName, $member.Username, $role.id, $member.id)
            }
            $apiChanges += @{
                'Switches'  = @{
                    'Call'      = ('/api/v2/authorization/roles/{0}/users/add' -f $role.id)
                    'Method'    = 'PUT'
                    'Body'      = $missing
                }
                'Log'       = $logEntries
            }
        }
        if ( $extra.Count -gt 0 )
        {
            $invalidUsers = $false
            $logEntries = @()
            foreach ( $id in $extra )
            {
                $memberName = $null
                if ( $usersById.ContainsKey($id) -eq $false )
                {
                    # Deleted users don't come back by default, try to look them up.
                    try
                    {
                        $res = Invoke-PureCloudWebRequest -Call:('/api/v2/users/{0}' -f $id) -Method:'GET' -WhatIf:$false -Confirm:$false
                    }
                    catch
                    {
                        $res = $null
                    }
                    $memberName = Get-DknPropertyValue -InputObject:$res -Path:'username'
                }
                else
                {
                    $member = $usersById[$id]
                    $memberName = Get-DknPropertyValue -InputObject:$member -Path:'username'
                }
                if ( [string]::IsNullOrEmpty($memberName) )
                {
                    $invalidUsers = $true
                    QUEUE_SOC_MESSAGE -Level:1 -Message:('User with ID {0} was not found, disabling role removal for "{1}"' -f $id, $roleName)
                }
                $logEntries += ('Type="RoleMemberRemove",RoleName="{0}",Username="{1}",RoleId="{2}",UserId="{3}"' -f $roleName, $memberName, $role.id, $id)
            }
            if ( $invalidUsers -eq $false )
            {
                $apiChanges += @{
                    'Switches'  = @{
                        'Call'      = ('/api/v2/authorization/roles/{0}/users/remove' -f $role.id)
                        'Method'    = 'PUT'
                        'Body'      = $extra
                    }
                    'Log'       = $logEntries
                }
            }
        }
    }

    #
    # Queue membership
    #
    foreach ( $queueName in $queues.psbase.Keys )
    {
        $queue = $queues[$queueName]
        $adGroupName = $queueAdGroup[$queueName]
        $actualMembers = $queueMembers[$queueName]
        $expectedMembers = @()
        foreach ( $member in $adGroupMembers[$adGroupName] )
        {
            $username = $member.$ADUsernameAttribute
            if ( [string]::IsNullOrEmpty($username) -or $missingUsers.ContainsKey($username) ) { continue }
            if ( $usersByEmail.ContainsKey($username) )
            {
                $expectedMembers += $usersByEmail[$username].id
            }
            else
            {
                QUEUE_SOC_MESSAGE -Level:1 -Message:('Unable to find user {0} ({1}) in PureCloud' -f $username, $member.Name)
            }
        }
        if ( $expectedMembers.Count -eq 0 )
        {
            throw ('Queue {0} ({1}) has no members, aborting script' -f $queueName, $adGroupName)
        }
        $missing    = @($expectedMembers    | Where-Object { $_ -notin $actualMembers      })
        $extra      = @($actualMembers      | Where-Object { $_ -notin $expectedMembers    })
        if ( $missing.Count -gt 0 )
        {
            $logEntries = @()
            if ( $missing.Count -gt 100 )
            {
                WriteOutput ('Type="Warning",QueueName="{0}",QueueId="{1}",MissingCount="{2}",Message="More than 100 members to add to queue, limiting call to PureCloud maximum of 100"' -f $queueName, $queue.id, $missing.Count)
                $missing = @($missing | Select-Object -First:100)
            }
            foreach ( $id in $missing )
            {
                $member = $usersById[$id]
                $usersThatShouldHavePhones[$id] = $true
                $logEntries += ('Type="QueueMemberAdd",QueueName="{0}",Username="{1}",QueueId="{2}",UserId="{3}"' -f $queueName, $member.Username, $queue.id, $member.id)
            }
            $apiChanges += @{
                'Switches'  = @{
                    'Call'      = ('/api/v2/routing/queues/{0}/users?delete=false' -f $queue.id)
                    'Method'    = 'POST'
                    'Body'      = @($missing | ForEach-Object { @{'id' = $_} })
                }
                'Log'       = $logEntries
            }
        }
        if ( $extra.Count -gt 0 )
        {
            $invalidUsers = $false
            $logEntries = @()
            if ( $extra.Count -gt 100 )
            {
                WriteOutput ('Type="Warning",QueueName="{0}",QueueId="{1}",ExtraCount="{2}",Message="More than 100 members to remove from queue, limiting call to PureCloud maximum of 100"' -f $queueName, $queue.id, $extra.Count)
                $extra = @($extra | Select-Object -First:100)
            }
            foreach ( $id in $extra )
            {
                $memberName = $null
                if ( $usersById.ContainsKey($id) -eq $false )
                {
                    # Deleted users don't come back by default, try to look them up.
                    try
                    {
                        $res = Invoke-PureCloudWebRequest -Call:('/api/v2/users/{0}' -f $id) -Method:'GET' -WhatIf:$false -Confirm:$false
                    }
                    catch
                    {
                        $res = $null
                    }
                    $memberName = Get-DknPropertyValue -InputObject:$res -Path:'username'
                }
                else
                {
                    $member = $usersById[$id]
                    $memberName = Get-DknPropertyValue -InputObject:$member -Path:'username'
                }
                if ( [string]::IsNullOrEmpty($memberName) )
                {
                    $invalidUsers = $true
                    QUEUE_SOC_MESSAGE -Level:1 -Message:('User with ID {0} was not found, disabling queue removal for "{1}"' -f $id, $queueName)
                }
                $logEntries += ('Type="QueueMemberRemove",QueueName="{0}",Username="{1}",QueueId="{2}",UserId="{3}"' -f $queueName, $memberName, $queue.id, $id)
            }
            if ( $invalidUsers -eq $false )
            {
                $apiChanges += @{
                    'Switches'  = @{
                        'Call'      = ('/api/v2/routing/queues/{0}/users?delete=true' -f $queue.id)
                        'Method'    = 'POST'
                        'Body'      = @($extra | ForEach-Object { @{'id' = $_} })
                    }
                    'Log'       = $logEntries
                }
            }
        }
    }

    #
    # PureCloud Directory Groups
    #
    $adDirGroupNames = @($adGroupByQueueId.psbase.Values)
    $pcDirGroupNames = @($pcDirGroupsByName.psbase.Keys)

    $missing = @($adDirGroupNames | Where-Object { $_ -notin $pcDirGroupNames })
    $extra   = @($pcDirGroupNames | Where-Object { $_ -notin $adDirGroupNames })

    foreach ( $dirGroupName in $extra )
    {
        $dirGroupId = $pcDirGroupsByName[$dirGroupName].id

        $apiChanges += @{
            'Switches'  = @{
                'Call'      = "/api/v2/groups/${dirGroupId}"
                'Method'    = 'DELETE'
            }
            'Log' = ('Type="DirGroupRemove",DirGroupName="{0}",DirGroupId="{1}"' -f $dirGroupName, $dirGroupId)
        }
    }
    foreach ( $dirGroupName in $missing )
    {
        $apiChanges += @{
            'Switches'  = @{
                'Call'      = "/api/v2/groups"
                'Method'    = 'POST'
                'Body'      = @{
                   'name'           = $dirGroupName
                   'type'           = 'official'
                   'rulesVisible'   = $true
                   'visibility'     = 'members'
                }
            }
            'Log' = ('Type="DirGroupAdd",DirGroupName="{0}"' -f $dirGroupName)
        }
    }
    #
    # PureCloud Directory Group membership
    #
    foreach ( $dirGroupName in $pcDirGroupNames )
    {
        $dirGroupId = $pcDirGroupsByName[$dirGroupName].id

        $pcDirGroupMembers = @($pcDirGroupMembersByName[$dirGroupName])
        $adDirGroupMembers = @(
            $adGroupMembers[$dirGroupName] |
            Select-Object -ExpandProperty:$ADUsernameAttribute |
            Where-Object { $usersByEmail.ContainsKey($_) } |
            ForEach-Object { $usersByEmail[$_].id }
        )

        $missing = @($adDirGroupMembers | Where-Object { $_ -notin $pcDirGroupMembers })
        $extra   = @($pcDirGroupMembers | Where-Object { $_ -notin $adDirGroupMembers })

        if ( $missing.Count -gt 0 )
        {
            $logEntries = @()

            if ( $missing.Count -gt 50 )
            {
                WriteOutput ('Type="Warning",DirGroupName="{0}",DirGroupId="{1}",MissingCount="{2}",Message="More than 50 members to add to group, limiting call to PureCloud maximum of 50"' -f $dirGroupName, $dirGroupId, $missing.Count)
                $missing = @($missing | Select-Object -First:50)
            }
            foreach ( $id in $missing )
            {
                $member = $usersById[$id]
                $logEntries += ('Type="DirGroupMemberAdd",DirGroupName="{0}",Username="{1}",DirGroupId="{2}",UserId="{3}"' -f $dirGroupName, $member.Username, $dirGroupId, $member.id)
            }
            $apiChanges += @{
                'Switches'  = @{
                    'Call'      = "/api/v2/groups/${dirGroupId}/members"
                    'Method'    = 'POST'
                    'Body'      = @{
                        'memberIds' = $missing
                        'version' = $pcDirGroupsByName[$dirGroupName].version
                    }
                }
                'Log'       = $logEntries
            }
        }
        if ( $extra.Count -gt 0 )
        {
            $logEntries = @()

            if ( $extra.Count -gt 25 )
            {
                WriteOutput ('Type="Warning",DirGroupName="{0}",DirGroupId="{1}",ExtraCount="{2}",Message="More than 25 members to remove from group, limiting call to PureCloud maximum of 25"' -f $dirGroupName, $dirGroupId, $extra.Count)
                $extra = @($extra | Select-Object -First:25)
            }
            foreach ( $id in $extra )
            {
                $member = $usersById[$id]
                $logEntries += ('Type="DirGroupMemberRemove",DirGroupName="{0}",Username="{1}",DirGroupId="{2}",UserId="{3}"' -f $dirGroupName, $member.Username, $dirGroupId, $member.id)
            }
            $apiChanges += @{
                'Switches'  = @{
                    'Call'      = "/api/v2/groups/${dirGroupId}/members?ids=" + ($extra -join ',')
                    'Method'    = 'DELETE'
                }
                'Log'       = $logEntries
            }
        }
    }

    #
    # Skills
    #
    $allQueueSkills = @(
        $queueSkills.psbase.Keys | `
        ForEach-Object { $queueSkills[$_] } | `
        Where-Object { [string]::IsNullOrEmpty($_) -eq $false } | `
        Select-Object -Unique
    )

    foreach ( $userId in $userSkills.psbase.Keys )
    {
        $user = $usersById[$userId]
        $username = $user.Username
        $queueIds = $userQueueMembership[$userid]

        $currentSkills = @(
            $userSkills[$userId] | `
            Where-Object { $_ -in $allQueueSkills }
        )

        $expectedSkills = @()
        foreach ( $queueId in $queueIds )
        {
            $expectedSkills += $queueSkills[$queueId]
        }

        $missing = @($expectedSkills | Where-Object { $_ -notin $currentSkills  })
        $extra   = @($currentSkills  | Where-Object { $_ -notin $expectedSkills })

        foreach ( $skillName in $missing )
        {
            $skillId = $skillsByName[$skillName].id
            $apiChanges += @{
                'Switches' = @{
                    'Call'      = "/api/v2/users/${userId}/routingskills"
                    'Method'    = 'POST'
                    'Body'      = @{
                        'id' = $skillId
                        'proficiency' = 0
                    }
                }
                'Log' = ('Type="SkillAdd",SkillName="{0}",Username="{1}",SkillId="{2}",UserId="{3}"' -f $skillName, $username, $skillId, $userId)
            }
        }

        foreach ( $skillName in $extra )
        {
            $skillId = $skillsByName[$skillName].id
            $apiChanges += @{
                'Switches' = @{
                    'Call'      = "/api/v2/users/${userId}/routingskills/${skillId}"
                    'Method'    = 'DELETE'
                }
                'Log' = ('Type="SkillRemove",SkillName="{0}",Username="{1}",SkillId="{2}",UserId="{3}"' -f $skillName, $username, $skillId, $userId)
            }
        }
    }

    #
    # Phones
    #

    # WebRTC Phones: Delete extra, or those with diffs (don't stuff around fixing diffs)
    foreach ( $phone in $phonesWebRtcById.psbase.Values )
    {
        $username = ''; $userid = ''; $hasDiff = $false
        if  ( (Test-Member -InputObject:$phone -Property:'webRtcUser') -and $null -ne $phone.webRtcUser )
        {
            $userid = $phone.webRtcUser.id
            $member = $usersById[$userid]
            $username = Get-DknPropertyValue -InputObject:$member -Path:'username'
            $agentName = Get-DknPropertyValue -InputObject:$member -Path:'name'
            $expectedPhoneName = '{0}{1}' -f $agentName, $WebRtcNameSuffix
            $hasDiff = $phone.name -cne $expectedPhoneName
        }
        if  ( $usersThatShouldHavePhones.ContainsKey($userid) -eq $false `
                -or $hasDiff )
        {
            $logEntries = @()
            $logEntries += ('Type="PhoneRemove",PhoneName="{0}",Username="{1}",PhoneId="{2}",UserId="{3}"' -f $phone.name, $username, $phone.id, $userid)
            if ( $phonesWebRtcByUser.ContainsKey($userid) ) { $phonesWebRtcByUser.Remove($userid) }
            $apiChanges += @{
                'Switches'  = @{
                    'Call'      = ('/api/v2/telephony/providers/edges/phones/{0}' -f $phone.id)
                    'Method'    = 'DELETE'
                }
                'Log'       = $logEntries
            }
        }
    }
    # WebRTC Phones: Create missing
    $missing = @($usersThatShouldHavePhones.psbase.Keys | Where-Object { $phonesWebRtcByUser.ContainsKey($_) -eq $false })
    foreach ( $userid in $missing )
    {
        $member = $usersById[$userid]
        if ( [string]::IsNullOrEmpty($username) -or $missingUsers.ContainsKey($member.Username) ) { continue }
        $phoneName = '{0}{1}' -f $member.name, $WebRtcNameSuffix
        $logEntries = @()
        $logEntries += ('Type="PhoneAdd",PhoneName="{0}",Username="{1}",UserId="{2}"' -f $phoneName, $member.Username, $member.id)
        $apiChanges += @{
            'Switches'  = @{
                'Call'      = '/api/v2/telephony/providers/edges/phones'
                'Method'    = 'POST'
                'Body'      =  @{
                                    'name'              = $phoneName
                                    'site'              = @{ 'id' = $site.id }
                                    'phoneBaseSettings' = @{ 'id' = $phoneBaseSettingsWebRtc.id }
                                    'lines'             = @(
                                        @{
                                            'lineBaseSettings'  = @{ 'id' = $lineBaseSettingsIdWebRtc }
                                            'template'          = @{ 'id' = $lineBaseSettingsIdWebRtc }
                                            'site'              = @{ 'id' = $site.id }
                                            'defaultForUser'    = @{ 'id' = $userid }
                                            'loggedInUser'      = @{ 'id' = $userid }
                                        }
                                    )
                                    'lineBaseSettings'  = @{ 'id' = $lineBaseSettingsIdWebRtc }
                                    'webRtcUser'        = @{ 'id' = $userid }
                                }
            }
            'PostHook'  = {
                if ( $null -eq $res ) { return }
                $userId = $res.webRtcUser.id
                $stationId = $res.lines[0].id
                Start-Sleep -Seconds:5
                Invoke-PureCloudWebRequest -Call:('/api/v2/users/{0}/station/defaultstation/{1}' -f $userId, $stationId) -Method:'PUT' | Out-Null
            }
            'Log'       = $logEntries
        }
    }

    ################################################################################
    #
    # Softphone software has issues with settings roaming between workstations.
    #
    ################################################################################

    ## Softphones: Sign out users (especially before deleting them as this messes stuff up), this allows agents to select the phone when hotdesking.
    #:nextSoftphone foreach ( $phone in $phonesSoftById.psbase.Values )
    #{
    #    if ( $phone.name.EndsWith($SoftphoneNameSuffix) -eq $false ) { continue }
    #    if ( $null -eq $phone.status -or $phone.status.operationalStatus -ne 'OFFLINE' ) { continue }
    #    if ( $phone.lines.Count -lt 1 ) { continue }
    #    foreach ( $line in $phone.lines )
    #    {
    #        if ( (Test-Member -InputObject:$line -Property:'loggedInUser') -eq $false ) { continue nextSoftphone }
    #        if ( $null -eq $line.loggedInUser ) { continue nextSoftphone }
    #
    #        $userid = $line.loggedInUser.id
    #        $username = 'unknown'
    #        if ( $usersById.ContainsKey($userid) ) { $username = $usersById[$userid].username }
    #
    #        $logEntries = @()
    #        $logEntries += ('Type="PhoneLogout",PhoneName="{0}",Username="{1}",PhoneId="{2}",UserId="{3}",LineId="{4}"' -f $phone.name, $username, $phone.id, $userid, $line.id)
    #        $apiChanges += @{
    #            'Switches'  = @{
    #                'Call'      = ('/api/v2/stations/{0}/associateduser' -f $line.id)
    #                'Method'    = 'DELETE'
    #            }
    #            'Log'       = $logEntries
    #        }
    #    }
    #}
    #
    ## Softphones: Delete extra, or those with diffs (don't stuff around fixing diffs)
    #$adCompKeys = @($agentAdComputers | ForEach-Object { $_['Name'] + '/' + $_['HardwareId'] })
    #$pcCompKeys = @()
    #foreach ( $phone in $phonesSoftById.psbase.Values )
    #{
    #    $hardwareId = $phone.properties.phone_hardwareId.value.instance
    #    $phoneKey = $phone.name + '/' + $hardwareId
    #    $pcCompKeys += $phoneKey
    #    if ( $phoneKey -notin $adCompKeys )
    #    {
    #        $logEntries = @()
    #        $logEntries += ('Type="PhoneRemove",PhoneName="{0}",HardwareId="{1}",PhoneId="{2}"' -f $phone.name, $hardwareId, $phone.id)
    #        $apiChanges += @{
    #            'Switches'  = @{
    #                'Call'      = ('/api/v2/telephony/providers/edges/phones/{0}' -f $phone.id)
    #                'Method'    = 'DELETE'
    #            }
    #            'Log'       = $logEntries
    #        }
    #    }
    #}
    ## Softphones: Create missing
    #$missing = @($adCompKeys | Where-Object { $_ -notin $pcCompKeys })
    #foreach ( $compKey in $missing )
    #{
    #    $phoneName, $hardwareId = $compKey -split '/'
    #    $logEntries = @()
    #    $logEntries += ('Type="PhoneAdd",PhoneName="{0}",HardwareId="{1}"' -f $phoneName, $hardwareId)
    #    $apiChanges += @{
    #        'Switches'  = @{
    #            'Call'      = '/api/v2/telephony/providers/edges/phones'
    #            'Method'    = 'POST'
    #            'Body'      =  @{
    #                                'name'                      = $phoneName
    #                                'site'                      = @{ 'id' = $site.id }
    #                                'phoneBaseSettings'         = @{ 'id' = $phoneBaseSettingsSoftphone.id }
    #                                'lines'                     = @(
    #                                    @{
    #                                        'name'              = '{0}_1' -f $phoneName.TrimEnd($SoftphoneNameSuffix)
    #                                        'lineBaseSettings'  = @{ 'id' = $lineBaseSettingsIdSoftphone }
    #                                    }
    #                                )
    #                                'lineBaseSettings'          = @{ 'id' = $lineBaseSettingsIdSoftphone }
    #                                'properties'                = @{
    #                                    'phone_hardwareId'      = @{
    #                                        'value'             = @{
    #                                            'instance'      = $hardwareId
    #                                        }
    #                                    }
    #                                }
    #                            }
    #        }
    #        'Log'       = $logEntries
    #    }
    #}

    # Remote Phones: Delete extra, or those with diffs (don't stuff around fixing diffs)
    #foreach ( $phone in $phonesRemoteById.psbase.Values )
    #{
    #    $username = ''; $userid = ''; $hasDiff = $false
    #    $addrs = @(Get-DknPropertyValue -InputObject:$phone -Path:'lines.properties.station_remote_address.value.instance')
    #    $skypeNumber = $null
    #    if ( $addrs.Count -eq 1 -and $usersByExtension.ContainsKey($addrs[0]) )
    #    {
    #        $skypeNumber = $addrs[0]
    #        $member = $usersByExtension[$addrs[0]]
    #        $userid = $member.id
    #        $username = $member.username
    #        $expectedPhoneName = '{0}{1}' -f $member.name, $RemotePhoneNameSuffix
    #        $hasDiff = $phone.name -cne $expectedPhoneName
    #    }
    #    if  ( $usersThatShouldHavePhones.ContainsKey($userid) -eq $false `
    #            -or $hasDiff )
    #    {
    #        $logEntries = @()
    #        $logEntries += ('Type="PhoneRemove",PhoneName="{0}",Username="{1}",PhoneId="{2}",UserId="{3}",SkypeNumber="{4}",DueToDiff="{5}"' -f $phone.name, $username, $phone.id, $userid, $skypeNumber, $hasDiff)
    #        if ( $phonesRemoteByUser.ContainsKey($userid) ) { $phonesRemoteByUser.Remove($userid) }
    #        $apiChanges += @{
    #            'Switches'  = @{
    #                'Call'      = ('/api/v2/telephony/providers/edges/phones/{0}' -f $phone.id)
    #                'Method'    = 'DELETE'
    #            }
    #            'Log'       = $logEntries
    #        }
    #    }
    #}
    ## Remote Phones: Create missing
    #foreach ( $userid in $usersThatShouldHavePhones.psbase.Keys )
    #{
    #    $member = $usersById[$userid]
    #    if ( $adUsers.ContainsKey($member.username) -eq $false ) { continue }
    #    $adUser = $adUsers[$member.username]
    #    $adUmEnabled = ($adUser.'msExchUmEnabledFlags' -band 1) -eq 1
    #    if ( $adUmEnabled )
    #    {
    #        QUEUE_SOC_MESSAGE -Level:1 -Message:('Agent {0} is UM enabled and has a Skype remote phone, this can result in call centre calls being routed to the agents voice mail' -f $member.username)
    #    }
    #}
    #$missing = @($usersThatShouldHavePhones.psbase.Keys | Where-Object { $phonesRemoteByUser.ContainsKey($_) -eq $false })
    #foreach ( $userid in $missing )
    #{
    #    $member = $usersById[$userid]
    #    if ( [string]::IsNullOrEmpty($username) -or $missingUsers.ContainsKey($member.username) ) { continue }
    #    if ( $extensionForUser.ContainsKey($member.username) -eq $false ) { continue }

    #    $extension = $extensionForUser[$member.username]
    #    $phoneName = '{0}{1}' -f $member.name, $RemotePhoneNameSuffix
    #    $logEntries = @()
    #    $logEntries += ('Type="PhoneAdd",PhoneName="{0}",Username="{1}",SkypeNumber="{2}"' -f $phoneName, $member.Username, $extension)
    #    $apiChanges += @{
    #        'Switches'  = @{
    #            'Call'      = '/api/v2/telephony/providers/edges/phones'
    #            'Method'    = 'POST'
    #            'Body'      =  @{
    #                                'name'                      = $phoneName
    #                                'site'                      = @{ 'id' = $site.id }
    #                                'phoneBaseSettings'         = @{ 'id' = $phoneBaseSettingsRemotePhone.id }
    #                                'lines'                     = @(
    #                                    @{
    #                                        'name'              = '{0}_SkypeRemote_1' -f ($member.name -replace '\s+')
    #                                        'lineBaseSettings'  = @{ 'id' = $lineBaseSettingsIdRemotePhone }
    #                                        'properties'                = @{
    #                                            'station_remote_address'= @{
    #                                                'value'             = @{
    #                                                    'instance'      = $extension
    #                                                }
    #                                            }
    #                                        }
    #                                    }
    #                                )
    #                                'lineBaseSettings'          = @{ 'id' = $lineBaseSettingsIdRemotePhone }
    #                            }
    #        }
    #        'PostHook'  = {
    #            if ( $null -eq $res ) { return }
    #            $extension = Get-DknPropertyValue -InputObject:$res -Path:'lines.properties.station_remote_address.value.instance'
    #            $userId = $usersByExtension[$extension].id
    #            $stationId = Get-DknPropertyValue -InputObject:$res -Path:'lines.id'
    #            if ( [string]::IsNullOrEmpty($userId) -or [string]::IsNullOrEmpty($stationId) )
    #            {
    #                WriteOutput ('Type="Warning",Action="DefaultStation",Message="Failed to set default station for extension {0} user {1} station {2}"' -f $extension, $userId, $stationId)
    #            }
    #            else
    #            {
    #                Start-Sleep -Seconds:5
    #                Invoke-PureCloudWebRequest -Call:('/api/v2/users/{0}/station/defaultstation/{1}' -f $userId, $stationId) -Method:'PUT' | Out-Null
    #            }
    #        }
    #        'Log'       = $logEntries
    #    }
    #}

    ################################################################################
    #
    # Perform actions
    #
    ################################################################################

    foreach ( $change in $apiChanges )
    {
        foreach ( $logEntry in $change['Log'] )
        {
            WriteOutput $logEntry
        }
        $switches = $change['Switches']
        $res = Invoke-PureCloudWebRequest @switches
        if ( $change.ContainsKey('PostHook') -and $change['PostHook'] -is [ScriptBlock] )
        {
            & $change['PostHook']
        }
    }

    # Clean up old state and save
    $toRemove = @($state['SyncPendingUsers'].psbase.Keys | Where-Object { $_ -notin $syncPendingUsers.psbase.Keys })
    foreach ( $key in $toRemove )
    {
        $state['SyncPendingUsers'].Remove($key)
        $stateChanged = $true
    }
    if ( $shouldFullDiff -and $script:_SLMessages.Count -eq 0 ) { $state['LastFullDiff'] = [DateTime]::Now; $stateChanged = $true }
    if ( $stateChanged )
    {
        $state | Save-DknStateFile -Confirm:$false
    }
}
catch
{
    QUEUE_SOC_MESSAGE -Level:3 -Message:('{0} at {1}:{2}' -f $_.Exception.Message, $_.InvocationInfo.ScriptName, $_.InvocationInfo.ScriptLineNumber)
}
finally
{
    REPORT_SOC_MESSAGES
    Stop-DknScriptLogging -Confirm:$false
}
# cSpell:ignore ivrs wgsw sccm isnot dnis rtcsip psbase adfs addrs