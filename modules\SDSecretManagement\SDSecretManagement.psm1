<#
.SYNOPSIS

Encryption and decryption of secrets.

#>

#region Exported functions
#
#

function Get-SDEncryptionCertificate
{
    <#
    .SYNOPSIS

    Get the certificate that should be used to perform encryption.

    .DESCRIPTION

    Typically Unprotect-SDSecret should be used to decrypt secrets.

    If no certificate is returned, the protect and unprotect cmdlets will use
    the Windows credential manager for the active user account.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([System.Security.Cryptography.X509Certificates.X509Certificate2])]
    Param
    (
    )

    Trap
    {
        throw $_
    }

    $subjectSortOrder =
    {
        switch -Regex ( $_.Subject )
        {
            'DeS-ServiceDesk' { 2; break }
            'DeS-' { 1; break }
            default { 0 }
        }
    }
    $validKeyUsages = 'Document Encryption', 'Key Encipherment'
    $certLocations = 'Cert:\LocalMachine\My', 'Cert:\CurrentUser\My'
    $certLocations |
        Get-ChildItem |
        Where-Object {
            $_.HasPrivateKey -eq $true -and
            $null -ne $_.EnhancedKeyUsageList -and
            $_.EnhancedKeyUsageList.Count -gt 0 -and
            $_.EnhancedKeyUsageList.FriendlyName -in $validKeyUsages
        } |
        Sort-Object -Property:$subjectSortOrder, 'NotAfter', 'Thumbprint' |
        Select-Object -Last:1

} # function Get-SDEncryptionCertificate

function New-SDEncryptionCertificate
{
    <#
    .SYNOPSIS

    Create a data encipherment certificate.

    .DESCRIPTION

    If no appropriate data encipherment certificate already exists, creates a
    new self signed data encipherment certificate in the machine store.

    Any administrator of the computer can access the key material by default.

    A data encipherment certificate is optional, the Windows credential manager
    is used by default if no data encipherment certificate is available.

    The data encipherment certificate is the best solution for servers as the
    secrets are available to all users who can access the certificate.

    The Windows credential manager limits the availability of the secret to the
    user account who generated the ciphertext of the secret.

    #>
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'Low')]
    [OutputType([System.Security.Cryptography.X509Certificates.X509Certificate2])]
    Param
    (
        [Parameter()]
        [ValidateSet('LocalMachine', 'CurrentUser')]
        [string]
        # Store the certificate for the user or computer?
        $StoreLocation = "LocalMachine",

        [Parameter()]
        [switch]
        # Allow exporting the private key.
        $AllowExport
    )

    Trap
    {
        throw $_
    }

    $existingCert = Get-SDEncryptionCertificate
    if ( $null -ne $existingCert )
    {
        # Return the existing certificate if one exists.
        return $existingCert
    }

    # Need to have some temporary files to work with
    $templateFile = New-TemporaryFile
    $reqFile = New-TemporaryFile
    # New temporary file creates the file, but certreq doesn't like that.
    if ( Test-Path -LiteralPath:$reqFile )
    {
        Remove-Item -LiteralPath:$reqFile -Confirm:$false -WhatIf:$false
    }

    # Configuration of the certificate
    $subjectName = 'DeS-ServiceDesk-DataEncipherment'

    $inf = @"
[Version]
Signature = "`$Windows NT`$"

[NewRequest]
Subject = "CN=$subjectName"
MachineKeySet = $($StoreLocation -eq 'LocalMachine')
KeyLength = 4096
KeySpec = AT_KEYEXCHANGE
HashAlgorithm = sha256
Exportable = ${AllowExport}
RequestType = Cert

KeyUsage = "CERT_KEY_ENCIPHERMENT_KEY_USAGE | CERT_DATA_ENCIPHERMENT_KEY_USAGE"
ValidityPeriod = "Years"
ValidityPeriodUnits = "20"

[EnhancedKeyUsageExtension]
OID=*******.4.1.311.80.1
"@
    Set-Content -LiteralPath:$templateFile -Value:$inf -Confirm:$false -WhatIf:$false
    Write-Verbose "Request:`n$inf"

    # Create the certificate and store in the LocalMachine/my certificate store.
    if ( $psCmdlet.ShouldProcess($StoreLocation, 'Create new self signed certificate CN=' + $subjectName) )
    {
        certreq.exe -new $templateFile $reqFile | Out-Null
    }

    # Clean up the temporary files
    if ( Test-Path -LiteralPath:$templateFile )
    {
        Remove-Item -LiteralPath:$templateFile -Confirm:$false -WhatIf:$false
    }
    if ( Test-Path -LiteralPath:$reqFile )
    {
        Remove-Item -LiteralPath:$reqFile -Confirm:$false -WhatIf:$false
    }

    # Return the certificate
    Get-SDEncryptionCertificate
} # function New-SDEncryptionCertificate

function Protect-SDSecret
{
    <#
    .SYNOPSIS

    Encrypt secrets for secure storage

    .DESCRIPTION

    Be aware this function makes no effort to secure the plain text string in
    memory.

    #>
    [CmdletBinding(SupportsShouldProcess = $false)]
    [OutputType([System.String])]
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute(
        'PSAvoidUsingConvertToSecureStringWithPlainText', '',
        Scope = 'Function', Target = 'Protect-SDSecret',
        Justification = 'Using Protect-CmsMessage to secure secrets'
    )]
    Param
    (
        # The plain text to encrypt.
        [Parameter()]
        [string]
        $Content,

        # Which mechanism to use to encrypt the data.
        # Default will use a certificate if available otherwise the Windows
        # credential manager.
        [Parameter()]
        [ValidateSet('Default', 'CredentialManager', 'Certificate')]
        [string]
        $Provider = 'Default'
    )

    Trap
    {
        throw $_
    }

    $cert = Get-SDEncryptionCertificate
    if ( $Provider -eq 'Default' )
    {
        # Issue any warnings
        if ( $null -ne $cert -and $null -eq $cert.PrivateKey )
        {
            Write-Warning (
                'A certificate is present, but the private key is not available. ' +
                'Will not use the certificate, ensure you are running elevated.'
            )
        }

        # Choose the provider
        if ( $null -ne $cert -and $null -ne $cert.PrivateKey )
        {
            # A certificate with a private key is available
            $Provider = 'Certificate'
        }
        else
        {
            $Provider = 'CredentialManager'
        }
    }

    switch ( $Provider )
    {
        'Certificate'
        {
            if ( $null -eq $cert )
            {
                throw ([InvalidOperationException]'No data encipherment certificate available')
            }
            $rex = "(`r`n|-----(BEGIN|END) CMS-----)"
            $cipherText = Protect-CmsMessage -Content:$Content -To:$cert
            $cipherText = $cipherText -replace $rex
        }
        'CredentialManager'
        {
            $cipherText = ConvertTo-SecureString -String:$Content -AsPlainText -Force |
                ConvertFrom-SecureString
        }
        default
        {
            throw ([NotImplementedException]'Provider not implemented')
        }
    }
    return $cipherText
} # function Protect-SDSecret

function Unprotect-SDSecret
{
    <#
    .SYNOPSIS

    Decrypt secrets previously encrypted with Protect-SDSecret

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType('Security.SecureString', 'Management.Automation.PSCredential')]
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute(
        'PSAvoidUsingConvertToSecureStringWithPlainText', '',
        Scope = 'Function', Target = 'Unprotect-SDSecret',
        Justification = 'Using Protect-CmsMessage to secure secrets'
    )]
    Param
    (
        # Encrypted text to be decrypted
        [Parameter()]
        [string]
        $Content,

        # If specified, will return a PSCredential with this user name set. If
        # not set, will return a simple SecureString.
        [Parameter()]
        [string]
        $UserName = ''
    )

    Trap
    {
        throw $_
    }

    $cred = $null
    # Strip any whitespace from the ciphertext, this allows better formatting.
    $Content = $Content -replace '[ \r\n]'

    # CMS (certificate) encrypted data always starts with MII
    if ( $Content -match '^MII' )
    {
        $cmsContent = @"
-----BEGIN CMS-----
$Content
-----END CMS-----
"@
        $cmsInfo = Get-CmsMessage -Content:$cmsContent
        $certInfo = '{0} (serial {1})' -f
            $cmsInfo.Recipients[0].IssuerName,
            $cmsInfo.Recipients[0].SerialNumber

        try
        {
            $cred = ConvertTo-SecureString -String:(Unprotect-CmsMessage -Content:$cmsContent) -AsPlainText -Force
        }
        catch [System.Security.Cryptography.CryptographicException]
        {
            $msg = 'Unable to decrypt secret'
            if ( [string]::IsNullOrWhiteSpace($UserName) -eq $false )
            {
                $msg += " for ${UserName}"
            }
            if ( $_.Exception.Message -match 'The enveloped-data message does not contain the specified recipient' )
            {
                $msg += ', requires certificate {0}, is it present?' -f
                    $certInfo
            }
            elseif ( $_.Exception.Message -match 'Keyset does not exist' )
            {
                $msg += ', requires certificate ' + $certInfo
                $msg += '. The key material is not available, check access (may require elevation). '
                $msg += 'Exception: ' + $_.Exception.Message
            }
            else
            {
                $msg += ', requires certificate {0}: {1}' -f
                    $certInfo,
                    $_.Exception.Message
            }

            throw $msg
        }
    }
    else
    {
        # Use the Windows credential manager
        $cred = ConvertTo-SecureString -String:$Content
    }

    if ( $null -eq $cred )
    {
        return
    }

    if ( [string]::IsNullOrWhiteSpace($UserName) )
    {
        return $cred
    }

    $params =
    @{
        'TypeName' = 'System.Management.Automation.PSCredential'
        'ArgumentList' =
        @(
            $UserName,
            $cred
        )
    }
    $psCred = New-Object @params
    return $psCred
} # function Unprotect-SDSecret

#endregion
#
#region Private helper functions
#
#

#endregion
#
#region Export members
#
#
#Requires -Version:5.0
Set-StrictMode -Version:5
$script:ErrorActionPreference = 'Stop'

Export-ModuleMember -Function:'Get-SDEncryptionCertificate'
Export-ModuleMember -Function:'New-SDEncryptionCertificate'
Export-ModuleMember -Function:'Protect-SDSecret'
Export-ModuleMember -Function:'Unprotect-SDSecret'

#endregion
