Configuration sdesk_purecloud_scripts
{
    Import-DscResource -ModuleName:'ComputerManagementDsc' -ModuleVersion:'8.5.0'
    Import-DscResource -ModuleName:'DeakinCustomDscResources'

    $biAdapterLocation = "C:\deakin\sdesk\purecloud-scripts\batch\run-bi-adapter.ps1"
    $taskPriorityProd = 6
    $taskPriorityDev = 8

    #region Genesys Cloud
    $cjisaCreds = Unprotect-DknCredential -Name:'cjisa' -Local
    $tasks =
    @{
        #'Adherence'         = @{ duration = "1.00:00:00"; interval =  "0" }
        'Aggregation'       = @{ duration =   "01:00:00"; interval = "30" }
        #'Chat'              = @{ duration =   "00:01:00"; interval =  "0" }
        #'Evaluation'        = @{ duration =   "00:30:00"; interval =  "0" }
        #'EvaluationCatchUp' = @{ duration =   "00:30:00"; interval =  "0" }
        'FactData'          = @{ duration =   "01:00:00"; interval =  "0" }
        #'HeadCountForecast' = @{ duration =   "01:00:00"; interval =  "0" }
        #'HoursBlockedData'  = @{ duration =   "01:00:00"; interval =  "0" }
        'Interaction'       = @{ duration =   "01:00:00"; interval =  "0" }
        #'OauthUsage'        = @{ duration = "1.00:00:00"; interval =  "0" }
        #'PresenceDetail'    = @{ duration =   "01:00:00"; interval =  "0" }
        #'OfferedForecast'   = @{ duration =   "01:00:00"; interval =  "0" }
        'Realtime'          = @{ duration =   "00:05:00"; interval =  "0" }
        #'ScheduleDetails'   = @{ duration =   "01:00:00"; interval =  "0" }
        #'Subscription'      = @{ duration = "1.00:00:00"; interval =  "0" }
        #'SubsUsers'         = @{ duration = "1.00:00:00"; interval =  "0" }
        #'TimeOffReq'        = @{ duration = "1.00:00:00"; interval =  "0" }
        #'VoiceAnalysis'     = @{ duration =   "01:00:00"; interval =  "0" }
        #'WFMAudit'          = @{ duration =   "01:00:00"; interval =  "0" }
        #'WFMSchedule'       = @{ duration =   "01:00:00"; interval =  "0" }
        #'CallJourney'       = @{ duration =   "01:00:00"; interval =  "0"; environment = "Prod" }
    }

    foreach ( $taskName in $tasks.Keys )
    {
        $environments = #'Dev',
                        'Prod'
        if ( $tasks.$taskName.ContainsKey('environment') )
        {
            $environments = $tasks.$taskName.environment
        }

        foreach ( $environment in $environments )
        {
            $duration = [TimeSpan]$tasks.$taskName.duration
            $interval = $tasks.$taskName.interval
            $taskPriority = $taskPriorityProd

            if ( $environment -eq 'Dev' )
            {
                $taskPriority = $taskPriorityDev
                if ( $duration -lt [TimeSpan]::FromDays(1) )
                {
                    $duration = [TimeSpan]::FromSeconds($duration.TotalSeconds * 10)
                }
            }
            if ( $taskName -eq 'Realtimev2' )
            {
                $arguments = "--logname bi-adapters-$environment-$taskName " + $biAdapterLocation + " "
                $arguments+= "$environment $taskName $interval"
                $startTime = [DateTime]'2016-01-01 20:00' + (Get-DknRandomOffset -MaxOffset:'1:00' -Name:$taskName)
                ScheduledTask "Genesys Cloud BI $environment $taskName"
                {
                    Ensure                  = 'Present'
                    TaskName                = "Genesys Cloud BI $environment $taskName"
                    TaskPath                = "\Service Desk $environment\"
                    Description             = 'https://wiki.deakin.edu.au/x/heyOD'
                    ActionExecutable        = 'c:\deakin\sdesk\StartPowerShellTask.cmd'
                    ActionArguments         = $arguments
                    ScheduleType            = 'Once'
                    StartTime               = $startTime
                    RepeatInterval          = $duration.ToString()
                    RepetitionDuration      = 'Indefinitely'
                    MultipleInstances       = 'IgnoreNew'
                    Priority                = $taskPriority
                    Compatibility           = 'Win8'
                    PsDscRunAsCredential    = $cjisaCreds.Credential
                }
            }
            else
            {
                $arguments = $biAdapterLocation + " "
                $arguments+= "$environment $taskName $interval"
                $startTime = [DateTime]'2016-01-01 20:00' + (Get-DknRandomOffset -MaxOffset:'1:00' -Name:$taskName)
                ScheduledTask "Genesys Cloud BI $environment $taskName"
                {
                    Ensure                  = 'Present'
                    TaskName                = "Genesys Cloud BI $environment $taskName"
                    TaskPath                = "\Service Desk $environment\"
                    Description             = 'https://wiki.deakin.edu.au/x/heyOD'
                    ActionExecutable        = 'C:\deakin\sdesk\pwsh7\pwsh.exe'
                    ActionArguments         = $arguments
                    ScheduleType            = 'Once'
                    StartTime               = $startTime
                    RepeatInterval          = $duration.ToString()
                    RepetitionDuration      = 'Indefinitely'
                    MultipleInstances       = 'IgnoreNew'
                    Priority                = $taskPriority
                    Compatibility           = 'Win8'
                    PsDscRunAsCredential    = $cjisaCreds.Credential
                }
            }
            dknScheduledTaskCredential "creds-cjisa Genesys Cloud BI $environment $taskName"
            {
                Ensure                          = 'Present'
                TaskName                        = "Genesys Cloud BI $environment $taskName"
                TaskPath                        = "\Service Desk $environment\"
                ExecuteAsProtectedCredential    = 'cjisa'
                DependsOn                       = "[ScheduledTask]Genesys Cloud BI $environment $taskName"
                LocalCredential                 = $true
            }
        }
    }
    #endregion

    #region Groups
    $taskName = 'Group BI prod'
    ScheduledTask $taskName
    {
        Ensure                  = 'Present'
        TaskName                = $taskName
        TaskPath                = '\Service Desk Prod\'
        ActionWorkingPath       = 'C:\deakin\sdesk\bi-adapters\group-bi-adapter'
        Description             = 'https://wiki.deakin.edu.au/x/heyOD'
        ActionExecutable        = 'C:\deakin\sdesk\pwsh7\pwsh.exe'
        ActionArguments         = '-file C:\deakin\sdesk\bi-adapters\group-bi-adapter\group-bi-adapter.ps1'
        ScheduleType            = 'Daily'
        StartTime               = [DateTime]'2016-01-01 05:45:52'
        MultipleInstances       = 'IgnoreNew'
        Priority                = $taskPriorityProd
        Compatibility           = 'Win8'
    }
    #endregion

    #region SOC checks
    $taskName = 'PURECLOUD-BI-REALTIME SOC check'
    ScheduledTask $taskName
    {
        Ensure                  = 'Present'
        TaskName                = $taskName
        TaskPath                = '\Service Desk Prod\'
        ActionWorkingPath       = 'C:\deakin\sdesk\purecloud-scripts\soc-checks'
        Description             = 'https://wiki.deakin.edu.au/x/heyOD'
        ActionExecutable        = 'C:\deakin\sdesk\pwsh7\pwsh.exe'
        ActionArguments         = '-file C:\deakin\sdesk\purecloud-scripts\soc-checks\PURECLOUD-BI-REALTIME.ps1'
        ScheduleType            = 'Weekly'
        DaysOfWeek              = 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'
        StartTime               = [DateTime]'2016-01-01 08:05:00'
        RepeatInterval          = '00:05:00'
        RepetitionDuration      = '09:50:00'
        MultipleInstances       = 'IgnoreNew'
        Priority                = $taskPriorityProd
        Compatibility           = 'Win8'
    }
    $taskName = 'GIT-STATUS SOC check'
    ScheduledTask $taskName
    {
        Ensure                  = 'Present'
        TaskName                = $taskName
        TaskPath                = '\Service Desk Prod\'
        ActionWorkingPath       = 'C:\deakin\sdesk\purecloud-scripts\soc-checks'
        Description             = 'https://wiki.deakin.edu.au/x/nx1rDg'
        ActionExecutable        = 'C:\deakin\sdesk\pwsh7\pwsh.exe'
        ActionArguments         = '-file C:\deakin\sdesk\purecloud-scripts\soc-checks\GIT-STATUS.ps1 -ReportStatus'
        ScheduleType            = 'Once'
        StartTime               = [DateTime]'2016-01-01 04:00' + (Get-DknRandomOffset -MaxOffset:'1:00' -Name:$taskName)
        RepeatInterval          = '02:00:00'
        RepetitionDuration      = 'Indefinitely'
        MultipleInstances       = 'IgnoreNew'
        Priority                = $taskPriorityProd
        Compatibility           = 'Win8'
    }
    #endregion
    #region Batch jobs
    $taskName = 'GENESYS-CLOUD-MAINTENANCE'
    ScheduledTask $taskName
    {
        Ensure                  = 'Present'
        TaskName                = $taskName
        TaskPath                = '\Service Desk Prod\'
        ActionWorkingPath       = 'C:\deakin\sdesk\purecloud-scripts\batch'
        Description             = 'https://wiki.deakin.edu.au/x/8EAGDg'
        ActionExecutable        = 'C:\deakin\sdesk\pwsh7\pwsh.exe'
        ActionArguments         = '-file C:\deakin\sdesk\purecloud-scripts\batch\GENESYS-CLOUD-MAINTENANCE.ps1'
        ScheduleType            = 'Once'
        StartTime               = [DateTime]'2016-01-01 00:55:00'
        RepeatInterval          = '00:30:00'
        RepetitionDuration      = 'Indefinitely'
        MultipleInstances       = 'IgnoreNew'
        Priority                = $taskPriorityProd
        Compatibility           = 'Win8'
    }
    #endregion
}
