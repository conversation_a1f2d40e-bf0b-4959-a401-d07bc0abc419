@{
    # Environments
    #   A list of environments to run against.
    #       Database        = Name of the Genesys historical database on the local server
    #       GenesysOrg      = The organisation name of the Genesys environment (to ensure connecting to correct env)
    #       ClientId        = OAuth client ID for the Genesys environment
    #       ClientSecret    = OAuth secret
    #       DisconnectLimit = Maximum conversations that will be disconnected in a single execution
    # To encrypt creds: Import-Module .\modules\SDSecretManagement; Protect-SDSecret 'pass'
    Environments =
    @(
        @{
            Database        = 'GenesysHistoricalData'
            GenesysOrg      = 'Deakin University'
            ClientId        = ''
            ClientSecret    = ''
            DisconnectLimit = 2
        }
        @{
            Database        = 'GenesysHistoricalDataDev'
            GenesysOrg      = 'Deakin University Dev'
            ClientId        = ''
            ClientSecret    = ''
            DisconnectLimit = 200
        }
    )
    # LogDirectory  = Base directory for logging, $null for no logging
    LogDirectory    = 'C:\logs\local\GENESYS-CLOUD-MAINTENANCE\'
    # SocqHostname  = The SOC host the service is attached to, i.e. pcc-mssql-f1.du
    SocqHostname    = 'pcc-mssql-f1.du'
    # SocqService   = The name of the SOC service, i.e. GENESYS-CLOUD-MAINTENANCE
    SocqService     = 'GENESYS-CLOUD-MAINTENANCE'
    # SocqUser      = SOCQ API account username
    SocqUser        = 'socq-purecloud'
    # SocqPass      = SOCQ API account password
    SocqPass        = ''
    # SocServerHost = Server FQDN hosting the SOCQ API, i.e. soc.deakin.edu.au
    SocServerHost   = 'soc.deakin.edu.au'
}
