# Overview

A PowerShell Cmdlet interface to Microsoft SQL Server.

## Cmdlets

| Cmdlet                         | Description                                                            |
|--------------------------------|------------------------------------------------------------------------|
| Complete-SDMSSQLTransaction    | Commit the active transaction.                                         |
| Connect-SDMSSQLDatabase        | Establish a connection to a MSSQL database.                            |
| Disconnect-SDMSSQLDatabase     | Close a connection to a database.                                      |
| Get-SDMSSQLConnection          | Get the most recently established database connection.                 |
| Initialize-SDMSSQLCommand      | Prepares an SQL command for execution.                                 |
| Invoke-SDMSSQLCommand          | Executes SQL statements.                                               |
| Invoke-SDMSSQLQuery            | Run a SQL query.                                                       |
| Start-SDMSSQLTransaction       | Start a database transaction.                                          |
| Test-SDMSSQLDatabaseConnection | Ensures the connection to the database is open and in a healthy state. |
| Undo-SDMSSQLTransaction        | Roll back the active transaction.                                      |
