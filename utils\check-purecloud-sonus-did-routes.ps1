<#
.SYNOPSIS

Checks/creates contacts in Active Directory that the Son<PERSON> uses for routing
calls to PureCloud.

.DESCRIPTION

The Sonus looks up contact objects in Active Directory to determine which
numbers to route to PureCloud.

AD OU:
    DU-DEV: OU=Purecloud,OU=Skype For Business,DC=du-dev,DC=deakin,DC=edu,DC=au
    DU:     OU=Purecloud,OU=Skype For Business,DC=du,DC=deakin,DC=edu,DC=au

The wg-purecloud-admin group has full control of contact objects in the above OUs.

The contact objects must have the following attributes:

-	primaryInternationalISDNNumber must be an e164 number with no spaces etc.
-	physicalDeliveryOfficeName  must be set to "PURECLOUD" (case sensitive
    without quotes)
-	The object class must be contact
-	The name and displayname can be anything you like. I used the name of the
    IVR but you can use something else. The Sonus will only use
    primaryInternationalISDNNumber.

Example object:

Get-ADObject -Identity "CN=eSolutions IVR,OU=Purecloud,OU=Skype For Business,DC=du-dev,DC=deakin,DC=edu,DC=au" `
             -Properties physicalDeliveryOfficeName,primaryInternationalISDNNumber,Name,cn,DisplayName

CN                             : eSolutions IVR
DisplayName                    : eSolutions IVR
DistinguishedName              : CN=eSolutions IVR,OU=Purecloud,OU=Skype For Business,DC=du-dev,DC=deakin,DC=edu,DC=au
Name                           : eSolutions IVR
ObjectClass                    : contact
ObjectGUID                     : d49de461-e17d-46c7-8795-3995c80e1647
physicalDeliveryOfficeName     : PURECLOUD
primaryInternationalISDNNumber : +***********


The Sonus builds its cache from the following LDAP filter:
    (&(objectClass=contact)(physicalDeliveryOfficeName=PURECLOUD)(primaryInternationalISDNNumber=*))

To reserve numbers but not route calls, create the contact but leave the
physicalDeliveryOfficeName attribute blank.

.LINK

https://wiki.deakin.edu.au/x/XZmvD

#>
[Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingWriteHost', '')]
[CmdletBinding()]
Param
(
)

$searchBase = "OU=Purecloud,OU=Skype For Business,DC=$($env:userdomain),DC=deakin,DC=edu,DC=au"
$params =
@{
    SearchBase  = $searchBase
    Filter      = {objectClass -eq 'contact'}
    Properties  = 'physicalDeliveryOfficeName', 'primaryInternationalISDNNumber'
}
$existing = @(Get-ADObject @params)

$active = $existing |
    Where-Object physicalDeliveryOfficeName -EQ 'PURECLOUD' |
    ForEach-Object primaryInternationalISDNNumber
$inactive = $existing |
    Where-Object physicalDeliveryOfficeName -NE 'PURECLOUD' |
    ForEach-Object primaryInternationalISDNNumber

$didPools = Invoke-PureCloudWebRequest -Call:'/api/v2/telephony/providers/edges/didpools'
$inPureCloud = @()
foreach ( $pool in $didPools.entities )
{
    $start = $pool.startPhoneNumber -replace '.+(\d{5})$','$1'
    $end = $pool.endPhoneNumber -replace '.+(\d{5})$','$1'
    $prefix = $pool.startPhoneNumber -replace '^(.+)\d{5}$','$1'
    foreach ( $num in $start..$end )
    {
        $e164 = "$prefix$num"
        $Name = "PureCloud IVR $num"
        Write-Host -ForegroundColor:'Gray' "Checking $num ($e164)"
        $inPureCloud += $e164
        if ( $e164 -in $active )
        {
            $exist = $existing | Where-Object primaryInternationalISDNNumber -EQ $e164
            Write-Host -ForegroundColor:'Green' ('    -> already present: ' + $exist.Name)
        }
        elseif ( $e164 -in $inactive )
        {
            $exist = $existing | Where-Object primaryInternationalISDNNumber -EQ $e164
            Write-Host -ForegroundColor:'Yellow' (
                '    -> already present but INACTIVE (physicalDeliveryOfficeName != PURECLOUD): ' + $exist.Name
            )
        }
        else
        {
            $params = @{
                Path = "OU=Purecloud,OU=Skype For Business,DC=$($env:userdomain),DC=deakin,DC=edu,DC=au"
                DisplayName = $Name
                Name = $Name
                Type = "contact"
                OtherAttributes = @{
                    'physicalDeliveryOfficeName' = 'PURECLOUD'
                    'primaryInternationalISDNNumber' = $e164
                }
            }
            New-ADObject @params
            Write-Host -ForegroundColor:'Cyan' ('    -> created: ' + $Name + ' -> ' + $e164)
        }
    }
}
$extra = @($existing | Where-Object { $_.primaryInternationalISDNNumber -NotIn $inPureCloud })
foreach ( $obj in $extra )
{
    Write-Host -ForegroundColor:'Red' (
        'Extra! Name: "{0}", number {1}' -f $obj.Name, $obj.primaryInternationalISDNNumber
    )
}
