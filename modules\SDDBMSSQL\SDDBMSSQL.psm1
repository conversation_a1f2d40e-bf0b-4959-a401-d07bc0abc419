<#
.SYNOPSIS

Provides cmdlets for working with MS SQL databases.

#>

#
#region Exported functions
function Complete-SDMSSQLTransaction
{
    <#
    .SYNOPSIS

    Commit the active transaction.

    .DESCRIPTION

    When a transaction is committed, the commands in the transaction are
    finalized and the data affected by the commands is changed.

    #>
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'Low')]
    Param
    (
        # The transaction to commit. If not specified, the last transaction
        # will be committed.
        [Parameter()]
        [Data.SqlClient.SqlTransaction]
        $Transaction = $script:lastTransaction
    )

    Trap
    {
        throw $_
    }

    if ( $null -eq $Transaction )
    {
        $msg = 'Cannot commit transaction. No transaction is active.'
        throw [InvalidOperationException]$msg
    }

    if ( $null -eq $Transaction.Connection )
    {
        $msg = 'Cannot commit transaction. Transaction is not active.'
        throw [InvalidOperationException]$msg
    }

    if ( $psCmdlet.ShouldProcess('Current transaction', 'commit') )
    {
        $Transaction.Commit()
    }
} # function Complete-SDMSSQLTransaction

function Connect-SDMSSQLDatabase
{
    <#
    .SYNOPSIS

    Establish a connection to a MS SQL database.

    .DESCRIPTION

    Connect to the specified database.

    The connection is held open until specifically closed, or the module is
    unloaded.

    A reference to the last opened connection is maintained to save passing the
    connection to every cmdlet. If multiple connections are in use, the
    connection reference from this cmdlet should be retained and used.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([Data.SqlClient.SqlConnection])]
    Param
    (
        # The MS SQL instance to connect to
        [Parameter(Mandatory = $true, ParameterSetName = 'InstanceName')]
        [Parameter(Mandatory = $true, ParameterSetName = 'ConnectionString')]
        [ValidateNotNullOrEmpty()]
        [string]
        $InstanceName,

        # The connection string used to connect to the database.
        [Parameter(Mandatory = $true, ParameterSetName = 'ConnectionString')]
        [ValidateNotNullOrEmpty()]
        [string]
        $ConnectionString,

        # Credentials used to connect to the database, if not provided will use
        # integrated authentication.
        [Parameter(ParameterSetName = 'InstanceName')]
        [Management.Automation.Credential()]
        [Management.Automation.PSCredential]
        $Credential = [Management.Automation.PSCredential]::Empty,

        # The default database
        [Parameter(ParameterSetName = 'InstanceName')]
        [Alias("Database")]
        [string]
        $DefaultDatabase,

        # Any additional properties to set in the connection string.
        # i.e. @{ 'TrustServerCertificate' = 'true' }
        [Parameter(ParameterSetName = 'InstanceName')]
        [hashtable]
        $AdditionalConnectionParameters = @{},

        # Minimum time (in seconds) to wait for a free connection from the pool.
        [Parameter(ParameterSetName = 'InstanceName')]
        [ValidateRange(1, 900)]
        [int]
        $ConnectionTimeoutSeconds,

        # Don't open the connection to the database.
        # Test-SDMSSQLDatabaseConnection can be used to test and open the
        # connection with the Repair parameter at a later stage.
        [Parameter()]
        [switch]
        $PrepareOnly
    )

    Trap
    {
        throw $_
    }

    if ( $PsCmdlet.ParameterSetName -eq 'ConnectionString' )
    {
        $params =
        @{
            'TypeName'      = 'Data.SqlClient.SqlConnectionStringBuilder'
            'ArgumentList'  = $ConnectionString
        }
        $builder = New-Object @params
        $connectString = $builder.ToString()
    }
    else
    {
        $builder = New-Object -TypeName:'Data.SqlClient.SqlConnectionStringBuilder'
        $builder['Data Source'] = $InstanceName
        if ( $null -eq $Credential -or $Credential -eq [Management.Automation.PSCredential]::Empty )
        {
            $builder['Integrated Security'] = $true
        }
        else
        {
            $builder['User Id'] = $Credential.UserName
            $builder['Password'] = $Credential.GetNetworkCredential().Password
        }
        if ( $PsBoundParameters.ContainsKey('DefaultDatabase') )
        {
            $builder['Initial Catalog'] = $DefaultDatabase
        }
        if ( $PsBoundParameters.ContainsKey('ConnectionTimeoutSeconds') )
        {
            $builder['Connection Timeout'] = $ConnectionTimeoutSeconds
        }
        foreach ( $key in $AdditionalConnectionParameters.PSBase.Keys )
        {
            $builder.Add($key, $AdditionalConnectionParameters[$key])
        }
        $connectString = $builder.ToString()
    }

    if ( $builder.ContainsKey('Password') -and [string]::IsNullOrEmpty($builder['Password']) -eq $false )
    {
        $builder['Password'] = '***'
    }
    $connectStringRedacted = $builder.ToString()

    Write-Verbose (
        'Connecting to {0} using connection string "{1}"' -f
            $builder['Data Source'],
            $connectStringRedacted
    )

    $params =
    @{
        TypeName        = 'Data.SqlClient.SqlConnection'
        ArgumentList    = $connectString
    }
    $connection = New-Object @params

    $builder = $null
    $connectString = $null
    $connectStringRedacted = $null

    if ( $PrepareOnly )
    {
        $script:lastConnection = $connection
        $script:lastConnection
        return
    }

    $maxAttempts = 3
    $sleepBetweenAttempts = [TimeSpan]::FromSeconds(5)
    for ( $attempt = 1 ; $attempt -le $maxAttempts ; $attempt++ )
    {
        try
        {
            $connection.Open()
            break
        }
        catch [Data.SqlClient.SqlException]
        {
            $exception = $_
            $retry = $false

            # Should the error be retried? Document clear cut cases.
            switch ( $exception.Exception.Number )
            {
                  -2    { $retry = $true }  # Timeout expired
            }

            if ( $attempt -ge $maxAttempts )
            {
                $retry = $false
            }
            $msg = 'Connect to database failed'
            if ( $retry )
            {
                $msg += ' (attempt {0}/{1})' -f
                    $attempt,
                    $maxAttempts
            }
            $msg += (
                ': {0} (#{1}) at {1}:{2}' -f
                    $exception.Exception.Message,
                    $exception.Exception.Number,
                    $exception.InvocationInfo.ScriptName,
                    $exception.InvocationInfo.ScriptLineNumber
            )

            Write-Warning $msg

            if ( -not $retry )
            {
                throw $exception
            }

            Start-Sleep -Seconds:$sleepBetweenAttempts.TotalSeconds
        }
    }

    $script:lastConnection = $connection
    $script:lastConnection
} # function Connect-SDMSSQLDatabase

function Disconnect-SDMSSQLDatabase
{
    <#
    .SYNOPSIS

    Close a connection to a database.

    .DESCRIPTION

    Close a connection to a database.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    Param
    (
        # The connection to close. If not specified, the most recently opened
        # connection will be closed.
        [Parameter()]
        [Data.SqlClient.SqlConnection]
        $Connection = $script:lastConnection
    )

    Trap
    {
        throw $_
    }

    if ( $null -eq $Connection ) { return }

    if ( $Connection.State -eq [Data.ConnectionState]::Closed )
    {
        return
    }

    Write-Verbose ('Closing connection to {0}' -f $Connection.DataSource)

    $Connection.Close() | Out-Null
    $Connection.Dispose() | Out-Null
    $script:lastConnection = $Connection = $null
} # function Disconnect-SDMSSQLDatabase

function Get-SDMSSQLConnection
{
    <#
    .SYNOPSIS

    Get the most recently established database connection.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([Data.SqlClient.SqlConnection])]
    Param
    (
    )

    Trap
    {
        throw $_
    }

    $script:lastConnection
} # function Get-SDMSSQLConnection

function Initialize-SDMSSQLCommand
{
    <#
    .SYNOPSIS

    Prepares an SQL command for execution.

    .DESCRIPTION

    Create a command that is ready to be executed. The command can be executed
    multiple times with differing bind parameters.

    The connection to the database server does not have to be open.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([Data.SqlClient.SqlCommand])]
    Param
    (
        # The SQL statement to prepare for execution. Do not dynamically string
        # build queries with user input (and avoid it otherwise), use the
        # BindParameter parameter where possible to avoid string building.
        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [string]
        $Query,

        # Parameters to provide to the statement.
        # Passing parameters avoids SQL injection vulnerabilities that occur
        # with building SQL statements dynamically as strings, especially when
        # user input is involved. The database will correctly handle escaping
        # any passed parameters.
        # Any parameters that are to be modified on execution must be specified
        # on initialisation also.
        [Parameter()]
        [ValidateNotNull()]
        [hashtable]
        $BindParameter = @{},

        # The connection to use. If not specified, the most recently opened
        # connection will be used.
        [Parameter()]
        [Data.SqlClient.SqlConnection]
        $Connection = $script:lastConnection,

        # The number of seconds to wait for execution before timing out.
        [Parameter()]
        [ValidateRange(1, 65535)]
        [int]
        $QueryTimeout = 120
    )

    Trap
    {
        throw $_
    }

    if ( $null -eq $Connection )
    {
        $msg = 'No database connection specified and no existing connection available'
        throw [InvalidOperationException]$msg
    }

    $sqlCmd = $Connection.CreateCommand()
    $sqlCmd.CommandText = $Query
    $sqlCmd.CommandTimeout = $QueryTimeout
    # $sqlCmd.BindByName = $true

    # Assign any passed bind parameters.
    foreach ( $parameterName in $BindParameter.PSBase.Keys )
    {
        $paramObject = [Data.SqlClient.SqlParameter]::new(
            $parameterName,
            $BindParameter[$parameterName]
        )
        [void] $sqlCmd.Parameters.Add($parameterName, $paramObject.SqlDbType)
    }

    return $sqlCmd
} # function Initialize-SDMSSQLCommand

function Invoke-SDMSSQLCommand
{
    <#
    .SYNOPSIS

    Executes SQL statements.

    .DESCRIPTION

    Output is either the actual rows or count of impacted rows based on if the
    AsQuery or AsNonQuery parameter is passed.

    .INPUTS

    A command can be piped into this cmdlet.

    .EXAMPLE

    Execute a basic SELECT query, returning rows as hashtables in an array.

    Initialize-SDMSSQLCommand -Query:"SELECT 'test' AS columnname FROM DUAL" | Invoke-SDMSSQLCommand -AsQuery

    Name                           Value
    ----                           -----
    COLUMNNAME                     test

    .EXAMPLE

    Execute a basic SELECT query, returning rows as hashtables in a hashtable.

    PS C:\>$cmd = Initialize-SDMSSQLCommand -Query:"SELECT 'testkey' AS primary_col, 'value' as othercol FROM DUAL"
    PS C:\>$params = @{
        Command = $cmd
        AsQuery = $true
        OutputCollectionType = 'HashtableUniqueKey'
        OutputCollectionKey = 'PRIMARY_COL'
    }
    PS C:\>$results = Invoke-SDMSSQLCommand @params
    PS C:\>$results

    Name                           Value
    ----                           -----
    testkey                        {OTHERCOL, PRIMARY_COL}

    PS C:\>$results['testkey']

    Name                           Value
    ----                           -----
    OTHERCOL                       value
    PRIMARY_COL                    testkey

    PS C:\>foreach ( $key in $results.PSBase.Keys ) { $row = $results[$key]; $row['OTHERCOL'] }
    value

    .EXAMPLE

    Start-SDMSSQLTransaction | Out-Null
    try
    {
        Initialize-SDMSSQLCommand -Query:"INSERT INTO TABLE (COL1, COL2) VALUES ('val1', 'val2')" |
            Invoke-SDMSSQLCommand -AsNonQuery

        Complete-SDMSSQLTransaction
    }
    catch
    {
        Undo-SDMSSQLTransaction -Verbose
    }

    Execute a statement in a transaction, committing if no exception is thrown,
    and rolling back otherwise.

    .EXAMPLE

    Demonstrate the use of bind parameters. The example initializes a command
    with two parameters, and then executes the statement multiple times,
    changing one of the parameters each loop.

    $params =
    @{
        Query = 'INSERT INTO TABLE (COL1, COL2) VALUES (:param1, :param2)'
        BindParameter = @{'param1' = 0; 'param2' = 'val2'}
    }
    $cmd = Initialize-SDMSSQLCommand @params
    for ( $i = 1; $i -le 5; $i++ )
    {
        Invoke-SDMSSQLCommand -Command:$cmd -AsNonQuery -BindParameter:@{'param1' = $i}
    }

    #>
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'Low', DefaultParameterSetName = 'Query')]
    [OutputType(([Hashtable], [Collections.ArrayList]), ParameterSetName = 'Query')]
    [OutputType([int], ParameterSetName = 'NonQuery')]
    Param
    (
        # A previously prepared command to use for execution. See
        # Initialize-SDMSSQLCommand.
        [Parameter(Mandatory = $true, ValueFromPipeline = $true)]
        [ValidateNotNullOrEmpty()]
        [Data.SqlClient.SqlCommand]
        $Command,

        # Execute the SQL statement as a query, returning rows (i.e. use for
        # SELECT).
        [Parameter(Mandatory = $true, ParameterSetName = 'Query')]
        [switch]
        $AsQuery,

        # Execute the SQL statement, returning a count of impacted rows (i.e.
        # use for INSERT, UPDATE, DELETE, EXEC).
        [Parameter(Mandatory = $true, ParameterSetName = 'NonQuery')]
        [switch]
        $AsNonQuery,

        # If this query or statement should be executed as part of a
        # transaction, pass the transaction from Start-SDMSSQLTransaction.
        [Parameter()]
        [Data.SqlClient.SqlTransaction]
        $Transaction = $null,

        # Parameters to provide to the statement.
        # Any parameters specified must have been declared when the command was
        # initialised.
        # Passing parameters avoids SQL injection vulnerabilities that occur
        # with building SQL statements dynamically as strings, especially when
        # user input is involved. The database will correctly handle escaping
        # any passed parameters.
        [Parameter()]
        [ValidateNotNull()]
        [hashtable]
        $BindParameter = @{},

        # The type of collection all rows are return as a member of.
        # HashtableUniqueKey
        #     Rows are placed into a hashtable object, with the expectation
        #     that the key will be unique across the data set. The key must be
        #     passed via the OutputCollectionKey parameter. If there are
        #     duplicate rows with the same key, only one row will be returned
        #     for the specified key with no warning.
        # Hashtable
        #     Rows are placed into an array, which is then placed into a
        #     hashtable object, i.e. there can be collisions on the key. The
        #     key must be passed via the OutputCollectionKey parameter.
        # Array
        #     Rows are placed into an array.
        [Parameter(ParameterSetName = 'Query')]
        [ValidateSet('HashtableUniqueKey', 'Hashtable', 'Array')]
        [string]
        $OutputCollectionType = 'Array',

        # The column to use as the hashtable key when OutputCollectionType is
        # either Hashtable or HashtableUniqueKey.
        [Parameter(ParameterSetName = 'Query')]
        [string]
        $OutputCollectionKey,

        # The data type each row is returned as.
        # Possible values: Hashtable, PSObject
        # Hashtables are significantly faster than PSObjects. PSObjects are
        # easier to filter, sort and pass to cmdlets like Export-Csv.
        [Parameter(ParameterSetName = 'Query')]
        [ValidateSet('Hashtable', 'PSObject')]
        [string]
        $OutputRowType = 'Hashtable',

        # Preserve the column order. Useful when the column order is important,
        # i.e. when directly outputting all columns to a .CSV file.
        # Retaining the columns order has a performance overhead.
        [Parameter(ParameterSetName = 'Query')]
        [switch]
        $RetainColumnOrder
    )

    Begin
    {
        Trap
        {
            throw $_
        }
    }

    Process
    {
        # Assign transaction
        if ( $PSBoundParameters.ContainsKey('Transaction') )
        {
            if ( $null -eq $Transaction )
            {
                $Transaction = $script:lastTransaction
            }

            if ( $null -eq $Transaction -or $null -eq $Transaction.Connection )
            {
                $msg = 'Cannot invoke SQL command. Transaction is not active.'
                throw [InvalidOperationException]$msg
            }

            $Command.Transaction = $Transaction
        }

        # Make sure the number of parameters passed in via BindParameter matches
        # the ones in the existing prepared statement.
        if ( $BindParameter.Count -ne $Command.Parameters.Count )
        {
            $msg = 'Number of parameters ({0}) does not match prepared statement ({1}).' -f
                $BindParameter.Count,
                $Command.Parameters.Count

            Write-Warning $msg
            Get-PSCallStack | Select-Object -ExpandProperty:'Location' | Out-Default

            throw [InvalidOperationException]$msg
        }

        # Assign any passed bind parameters.
        foreach ( $parameterName in $BindParameter.PSBase.Keys )
        {
            if ( $Command.Parameters.Contains($parameterName) -eq $false )
            {
                $msg = 'Use Initialize-SDMSSQLCommand when passing parameters'
                throw [InvalidOperationException]$msg
            }
            $Command.Parameters.Item($parameterName).Value = $BindParameter[$parameterName]
        }

        if ( $AsNonQuery )
        {
            if ( $psCmdlet.ShouldProcess('Database connection', (Format-IntCommandText -Command:$Command)) )
            {
                return $Command.ExecuteNonQuery()
            }
            return 0
        }

        $results = $null
        switch ( $OutputCollectionType )
        {
            'Array'
            {
                $results = New-Object -TypeName:'Collections.ArrayList'
            }
            'HashtableUniqueKey'
            {
                $results = @{}
            }
            'Hashtable'
            {
                $results = @{}
            }
        }

        $cmdText = $null
        try
        {
            if ( $psCmdlet.ShouldProcess('Database connection', $Command.CommandText) )
            {
                $reader = $Command.ExecuteReader()
            }
            else
            {
                return $results
            }
        }
        catch
        {
            $cmdText = Format-IntCommandText -Command:$Command

            $msg = (
                'SQL query did not succeed; {0}, query: {1}' -f
                    $_.Exception.Message,
                    $cmdText
            )

            throw [Exception]::new($msg, $_.Exception)
        }

        $colNames = @()
        $fieldCount = $reader.VisibleFieldCount
        for ( $fieldId = 0; $fieldId -lt $fieldCount; $fieldId++ )
        {
            $colNames += $reader.GetName($fieldId)
        }

        $readerTimerReport = [Diagnostics.Stopwatch]::StartNew()
        $readerTimerTotal = [Diagnostics.Stopwatch]::StartNew()
        $readerReportTime = [TimeSpan]::FromMinutes(2)
        $countRowsLoaded = 0
        foreach ( $row in $reader )
        {
            # Log slow queries to help with debugging
            if ( $readerTimerReport.Elapsed -gt $readerReportTime )
            {
                $runningTime = [TimeSpan]::FromSeconds(
                    [Math]::Round($readerTimerTotal.Elapsed.TotalSeconds, 0)
                ).ToString()
                if ( $null -eq $cmdText )
                {
                    $cmdText = Format-IntCommandText -Command:$Command
                }
                Write-Warning (
                    '{0} Query running for {1}, {2} rows loaded {3}' -f
                        [DateTime]::Now.ToString('s'),
                        $runningTime,
                        $countRowsLoaded,
                        $cmdText
                )
                $readerTimerReport.Restart()
            }
            if ( $RetainColumnOrder )
            {
                $rowData = [ordered]@{}
            }
            else
            {
                $rowData = @{}
            }
            foreach ( $col in $colNames )
            {
                $val = $row[$col]
                if ( [DBNull]::Value.Equals($val) )
                {
                    $val = $null
                }
                $rowData[$col] = $val
            }
            if ( $OutputRowType -eq 'PSObject' )
            {
                $rowData = [PSCustomObject] $rowData
            }

            switch ( $OutputCollectionType )
            {
                'Array'
                {
                    [void] $results.Add($rowData)
                }
                'HashtableUniqueKey'
                {
                    $key = [string] $rowData[$OutputCollectionKey]
                    $results[$key] = $rowData
                }
                'Hashtable'
                {
                    $key = [string] $rowData[$OutputCollectionKey]
                    if ( $results.ContainsKey($key) -eq $false )
                    {
                        $results[$key] = New-Object -TypeName:'Collections.ArrayList'
                    }
                    [void] $results[$key].Add($rowData)
                }
            }
            $countRowsLoaded++
        }
        if ( $reader.IsClosed -eq $false )
        {
            $reader.Close()
        }

        $results
    }
} # function Invoke-SDMSSQLCommand

function Invoke-SDMSSQLQuery
{
    <#
    .SYNOPSIS

    Run a SQL query.

    .DESCRIPTION

    Simple method to execute a SQL query. It is not recommended to use this
    cmdlet in loops as it is slower than Initialize-SDMSSQLCommand and
    Invoke-SDMSSQLCommand which are designed to be used in loops.

    .INPUTS

    None

    .EXAMPLE

    Invoke-SDMSSQLQuery -Query:'SELECT * FROM ACCOUNTS_V'

    Executes a basic SELECT query, returning a simple array with each row as a
    hashtable.

    Example of accessing the data:

    foreach ( $account in $results ) { Write-Host $account['USERNAME'] }

    .EXAMPLE

    Execute a basic SELECT query, returning a hashtable with a column as the key.

    $params =
    @{
        Query = 'SELECT * FROM ACCOUNTS_V'
        OutputCollectionType = 'HashtableUniqueKey'
        OutputCollectionKey = 'ACCOUNT_ID'
    }
    Invoke-SDMSSQLQuery @params

    Example of accessing the data:

    foreach ( $accountId in $results.PSBase.Keys )
    {
        $account = $results[$accountId]
        Write-Host $account['USERNAME']
    }

    .EXAMPLE

    Execute a basic SELECT query with bind parameters, returning an array of
    PSObjects.

    $params =
    @{
        Query = 'SELECT ACCOUNT_ID, USERNAME, SURNAME FROM ACCOUNTS_V WHERE ACCOUNT_ID=:ACCT_ID'
        OutputRowType = 'PSObject'
        BindParameter = @{'ACCT_ID' = 316396}
    }
    Invoke-SDMSSQLQuery @params

    USERNAME ACCOUNT_ID SURNAME
    -------- ---------- -------
    pfield       316396 Field

    #>
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'None')]
    [OutputType(([Hashtable], [Collections.ArrayList]))]
    Param
    (
        # The SQL statement to run. Do not dynamically string build queries
        # with user input (and avoid it otherwise), use the BindParameter
        # parameter where possible to avoid string building.
        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [string]
        $Query,

        # Parameters to provide to the statement. Passing parameters avoids SQL
        # injection vulnerabilities that occur with building SQL statements
        # dynamically as strings, especially when user input is involved. The
        # database will correctly handle escaping any passed parameters.
        [Parameter()]
        [ValidateNotNull()]
        [hashtable]
        $BindParameter = @{},

        # The type of collection all rows are return as a member of.
        # HashtableUniqueKey
        #     Rows are placed into a hashtable object, with the expectation
        #     that the key will be unique across the data set. The key must be
        #     passed via the OutputCollectionKey parameter. If there are
        #     duplicate rows with the same key, only one row will be returned
        #     for the specified key with no warning.
        # Hashtable
        #     Rows are placed into an array, which is then placed into a
        #     hashtable object, i.e. there can be collisions on the key. The
        #     key must be passed via the OutputCollectionKey parameter.
        # Array
        #     Rows are placed into an array.
        [Parameter()]
        [ValidateSet('HashtableUniqueKey', 'Hashtable', 'Array')]
        [string]
        $OutputCollectionType = 'Array',

        # The column to use as the hashtable key when OutputCollectionType is
        # either Hashtable or HashtableUniqueKey.
        [Parameter()]
        [string]
        $OutputCollectionKey,

        # The data type each row is returned as.
        # Possible values: Hashtable, PSObject
        # Hashtables are significantly faster than PSObjects. PSObjects are
        # easier to filter, sort and pass to cmdlets like Export-Csv.
        [Parameter()]
        [ValidateSet('Hashtable', 'PSObject')]
        [string]
        $OutputRowType = 'Hashtable',

        # Preserve the column order. Useful when the column order is important,
        # i.e. when directly outputting all columns to a .CSV file.
        # Retaining the columns order has a performance overhead.
        [Parameter()]
        [switch]
        $RetainColumnOrder
    )

    Trap
    {
        throw $_
    }

    # Connect to the database if required.
    $connection = $script:lastConnection
    if ( $null -eq $connection )
    {
        throw [InvalidOperationException]'No database connection'
    }

    # Make sure connection is healthy.
    $params =
    @{
        Connection              = $connection
        Repair                  = $true
        TerminateWhenUnhealthy  = $true
    }
    Test-SDMSSQLDatabaseConnection @params | Out-Null

    # Initialize a command.
    $params =
    @{
        Connection      = $connection
        Query           = $Query
        BindParameter   = $BindParameter
    }
    $cmd = Initialize-SDMSSQLCommand @params

    # Execute the command.
    $params =
    @{
        Command                 = $cmd
        AsQuery                 = $true
        OutputCollectionType    = $OutputCollectionType
        OutputCollectionKey     = $OutputCollectionKey
        OutputRowType           = $OutputRowType
        RetainColumnOrder       = $RetainColumnOrder
        BindParameter           = $BindParameter
    }
    Invoke-SDMSSQLCommand @params
} # function Invoke-SDMSSQLQuery

function Start-SDMSSQLTransaction
{
    <#
    .SYNOPSIS

    Start a database transaction.

    .DESCRIPTION

    Starts a transaction on the specified database connection.

    Only one transaction can be active at a time.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute(
        'PSUseShouldProcessForStateChangingFunctions',
        '',
        Scope = 'Function',
        Target = 'Start-SDMSSQLTransaction',
        Justification = 'Does not change state'
    )]
    [OutputType('SqlClient.SqlTransaction')]
    Param
    (
        # The connection to start the transaction on. If not specified, the
        # most recently opened connection will be closed.
        [Parameter()]
        [Data.SqlClient.SqlConnection]
        $Connection = $script:lastConnection,

        # See https://docs.microsoft.com/en-us/dotnet/api/system.data.isolationlevel
        [Parameter()]
        [Data.IsolationLevel]
        $IsolationLevel = [Data.IsolationLevel]::ReadCommitted
    )

    Trap
    {
        throw $_
    }

    if ( $null -eq $Connection )
    {
        $msg = 'No database connection specified and no existing connection available'
        throw [InvalidOperationException]$msg
    }

    $script:lastTransaction = $Connection.BeginTransaction($IsolationLevel)

    $script:lastTransaction
} # function Start-SDMSSQLTransaction

function Test-SDMSSQLDatabaseConnection
{
    <#
    .SYNOPSIS

    Ensures the connection to the database is open and in a healthy state.

    .DESCRIPTION

    Checks if the database connection is open, and if not, attempts to connect.
    The purpose is to allow easy reconnection to the database if the connection
    is interrupted.

    Only works if previously explicitly connected to the database, and have not
    explicitly disconnected from the database.

    .PARAMETER Connection

    The connection to test. If not specified, the most recently opened
    connection will be used.

    .PARAMETER WaitForAvailableSeconds

    Number of seconds to wait for "Executing", "Fetching", and "Connecting"
    states to complete.

    .PARAMETER Repair

    If the connection is disconnected or broken, attempt to reconnect.

    .PARAMETER TerminateWhenUnhealthy

    If the connection is not in a good state (after repair if chosen) then
    throw a terminating exception.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([Boolean])]
    Param
    (
        [Parameter()]
        [Data.SqlClient.SqlConnection]
        $Connection = $script:lastConnection,

        [Parameter()]
        [ValidateRange(0, 900)]
        [int]
        $WaitForAvailableSeconds = 30,

        [Parameter()]
        [switch]
        $Repair,

        [Parameter()]
        [switch]
        $TerminateWhenUnhealthy
    )

    Trap
    {
        throw $_
    }

    if ( $null -eq $Connection ) { return $false }

    $state = $false
    $retry = $false
    $reconnect = $false

    $timer = [Diagnostics.Stopwatch]::StartNew()
    do
    {
        $state = $false
        $retry = $false
        $reconnect = $false

        switch ( $Connection.State )
        {
            ([Data.ConnectionState]::Broken) { $reconnect = $Repair }
            ([Data.ConnectionState]::Closed) { $reconnect = $Repair }

            ([Data.ConnectionState]::Open) { $state = $true }

            ([Data.ConnectionState]::Connecting) { $retry = $true }
            ([Data.ConnectionState]::Executing) { $retry = $true }
            ([Data.ConnectionState]::Fetching) { $retry = $true }

            default
            {
                Write-Warning (
                    'Database connection to {0} is in an unknown state ({1})' -f
                        $Connection.DataSource,
                        $Connection.State
                )
            }
        }

        if ( $state -eq $true ) { break }

        # If the data source is not set, a reconnection cannot occur, and the
        # connection can't be busy.
        if ( [string]::IsNullOrEmpty($Connection.DataSource) )
        {
            break
        }

        if ( $reconnect )
        {
            $retry = $true
            Write-Warning (
                'Database connection to {0} is {1}, attempting to connect...' -f
                    $Connection.DataSource,
                    $Connection.State
            )
            $Connection.Open()
            $state = $true
        }

        if ( $retry -eq $false ) { break }

        Write-Warning (
            'Database connection to {0} is {1}, waiting...' -f
                $Connection.DataSource,
                $Connection.State
        )
        Start-Sleep -Milliseconds:500
    }
    while ( $timer.Elapsed.TotalSeconds -lt $WaitForAvailableSeconds )

    if ( $TerminateWhenUnhealthy -and $state -eq $false )
    {
        $dataSource = $Connection.DataSource
        if ( [string]::IsNullOrEmpty($dataSource) )
        {
            $dataSource = 'Database'
        }
        $msg = (
            'Connection to {0} is {1}' -f
                $dataSource,
                $Connection.State
        )
        throw [InvalidOperationException]$msg
    }

    return $state
} # function Test-SDMSSQLDatabaseConnection

function Undo-SDMSSQLTransaction
{
    <#
    .SYNOPSIS

    Roll back the active transaction.

    .DESCRIPTION

    When a transaction is rolled back, the changes that were made by the
    commands in the transaction are discarded and the data is restored to its
    original form.

    #>
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'None')]
    Param
    (
        # The transaction to roll back. If not specified, the last transaction
        # will be rolled back.
        [Parameter()]
        [Data.SqlClient.SqlTransaction]
        $Transaction = $script:lastTransaction
    )

    Trap
    {
        throw $_
    }

    # Always roll back, even in WhatIf mode. The following line outputs verbose
    # and what if output log lines.
    $psCmdlet.ShouldProcess('Current transaction', 'roll back') | Out-Null

    if ( $null -eq $Transaction )
    {
        $msg = 'Cannot roll back transaction. No transaction is active.'
        # throw [InvalidOperationException]$msg
        Write-Warning $msg
        return
    }

    if ( $null -eq $Transaction.Connection )
    {
        $msg = 'Cannot roll back transaction. Connection is not active.'
        # throw [InvalidOperationException]$msg
        Write-Warning $msg
        return
    }

    $connectionState =
        $Transaction.Connection.State -ne ([Data.ConnectionState]::Broken) -and
        $Transaction.Connection.State -ne ([Data.ConnectionState]::Closed)

    if ( $connectionState -eq $false )
    {
        $msg = 'Cannot roll back transaction. Connection is {0}.' -f
            $Transaction.Connection.State
        # throw [InvalidOperationException]$msg
        Write-Warning $msg
        return
    }

    $Transaction.RollBack()
    $Transaction.Dispose()
    $script:lastTransaction = $Transaction = $null
} # function Undo-SDMSSQLTransaction

#endregion Exported functions

#
#region Private helper functions
function Format-IntCommandText
{
    <#
    .SYNOPSIS

    Build a string of the command text and parameters for logging.

    .PARAMETER Command

    The command of which to format the command text.

    #>
    [CmdletBinding(SupportsShouldProcess = $false)]
    Param
    (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [Data.SqlClient.SqlCommand]
        $Command
    )

    $msg = '"{0}"' -f $Command.CommandText

    if ( $Command.Parameters.Count -gt 0 )
    {
        $msg += ', {0} parameter(s) ' -f $Command.Parameters.Count
        $msg += @(
            $Command.Parameters |
                ForEach-Object { '{0}="{1}"' -f $_.ParameterName, $_.Value }
        ) -join ', '
    }

    return $msg
} # Format-IntCommandText
#endregion Private helper functions

#
#region Initialisation and export members
#Requires -Version 5.1
Set-StrictMode -Version:5
$script:ErrorActionPreference = 'Stop'

# Hold the most recent connection and transaction in a private variable.
$script:lastConnection = $null
$script:lastTransaction = $null

Export-ModuleMember -Function:'Complete-SDMSSQLTransaction'
Export-ModuleMember -Function:'Connect-SDMSSQLDatabase'
Export-ModuleMember -Function:'Disconnect-SDMSSQLDatabase'
Export-ModuleMember -Function:'Get-SDMSSQLConnection'
Export-ModuleMember -Function:'Initialize-SDMSSQLCommand'
Export-ModuleMember -Function:'Invoke-SDMSSQLCommand'
Export-ModuleMember -Function:'Invoke-SDMSSQLQuery'
Export-ModuleMember -Function:'Start-SDMSSQLTransaction'
Export-ModuleMember -Function:'Test-SDMSSQLDatabaseConnection'
Export-ModuleMember -Function:'Undo-SDMSSQLTransaction'
#endregion Initialisation and export members

