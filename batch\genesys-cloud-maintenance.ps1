<#
.SYNOPSIS

Perform maintenance tasks for Genesys Cloud.

.DESCRIPTION

Performs the following maintenance tasks:
    * Logging out inactive users (to save on platform licensing)
    * Disconnecting interactions left in the after call work state (for reporting purposes)
    * Disconnecting old interactions in development

Requires the following Genesys Cloud API permissions.

    API endpoint                                 | Permissions
    ---------------------------------------------|
    GET     /api/v2/organizations/me             | None
    GET     /api/v2/users/{0}                    | None
    POST    /api/v2/audits/query/realtime        | audits:audit:view
    DELETE  /api/v2/apps/users/{0}/logout        | oauth:token:delete (Not documented)
    GET     /api/v2/conversations/{0}            | conversation:communication:view
    POST    /api/v2/conversations/{0}/disconnect | conversation:communication:disconnect

.LINK

    https://wiki.deakin.edu.au/x/8EAGDg

#>
[CmdletBinding(SupportsShouldProcess = $true)]
Param
(
)

#Requires -Version:5
Set-StrictMode -Version:5
$ErrorActionPreference = 'Stop'
Import-Module -Name:"${PSScriptRoot}\..\modules\SDSecretManagement" -Verbose:$false
Import-Module -Name:"${PSScriptRoot}\..\modules\SDSOCQ" -Verbose:$false
Import-Module -Name:"${PSScriptRoot}\..\modules\SDGenesysCloud" -Verbose:$false
Import-Module -Name:"${PSScriptRoot}\..\modules\SDDBMSSQL" -Verbose:$false

$config = Import-LocalizedData

$sqlInactiveUsers = "
SELECT
     vwRealTimeUser.userid
    ,vwRealTimeUser.name
    ,vwRealTimeUser.systempresence
    ,vwRealTimeUser.routingstatus
    ,vwRealTimeUser.AgentStatus
    ,vwRealTimeUser.adherencestate
    ,vwRealTimeUser.AgentTime
    ,CAST(vwRealTimeUser.AgentTime AS FLOAT)/60/60 as TimeHours
    ,vwUserDetail.agentname
    ,vwUserDetail.department
FROM
    vwRealTimeUser
    LEFT OUTER JOIN vwUserDetail ON vwRealTimeUser.userid = vwUserDetail.id
WHERE
    -- On queue but not responding
    (
        AgentTime               > 1 * 60 * 60
        AND (
                systempresence  = 'ON_QUEUE'
            AND routingstatus   = 'NOT_RESPONDING'
            AND AgentStatus     = 'NOT_RESPONDING'
        )
    )

    -- Logged in, but not on-queue, talking, in-adherence or in a break
    OR (
            AgentTime           > 1 * 60 * 60
        AND systempresence      NOT IN ('OFFLINE', 'ON_QUEUE', 'MEAL', 'BREAK')
        AND AgentStatus         != 'TALKING'
        AND (
                adherencestate  IS NULL
            OR  adherencestate  != 'InAdherence'
        )
    )

    -- Maximum session duration
    OR (
            AgentTime           > 10 * 60 * 60
        AND systempresence      NOT IN ('OFFLINE')
        AND AgentStatus         NOT IN ('OFFLINE', 'TALKING')
        AND (
                adherencestate  IS NULL
            OR  adherencestate  != 'InAdherence'
        )
    )

ORDER BY
   AgentTime DESC
"

$sqlConversationsToDisconnect = "
SELECT
     conversationid
    ,vwQueueConvRealTime.agentname
    ,vwQueueDetails.name
    ,actingas
    ,direction
    ,media
    ,statusSecs
FROM
    vwQueueConvRealTime
    LEFT OUTER JOIN vwUserDetail   ON vwQueueConvRealTime.userid      = vwUserDetail.id
    LEFT OUTER JOIN vwQueueDetails ON vwQueueConvRealTime.queueid = vwQueueDetails.id
WHERE
    /*-- In after call work
    (
            statusSecs      > 4 * 60 * 60
        AND status          = 'ACW'
    )

    -- Present in development
    OR */ (
            statusSecs      > 12 * 60 * 60
        AND DB_NAME() != 'GenesysHistoricalData'
        AND UPPER(DB_NAME()) LIKE '%DEV%'
    )
ORDER BY statusSecs DESC
"

$sqlAuditInsertQuery = "
INSERT INTO [CSIOT].dbo.[GENESYS-CLOUD-MAINTENANCE] (
    [Time],
    [Organisation],
    [Action],
    [Object],
    [Duration]
)
VALUES
(
    @p_time,
    @p_organisation,
    @p_action,
    @p_object,
    @p_duration
)"
$sqlAuditInsertCommand = $null

$logPath = $null
if ( [string]::IsNullOrEmpty($config.LogDirectory) -eq $false )
{
    $scriptName = ([IO.FileInfo]$script:MyInvocation.MyCommand.Path).BaseName
    $logPath = Join-Path -Path:$config['LogDirectory'] -ChildPath:(
        $scriptName + '.' + [DateTime]::Now.ToString('yyyy-MM-dd-HH-mm') + '.log'
    )
    Start-Transcript -Path:$logPath -Append
}
try
{
    $params =
    @{
        SocServerHost       = $config.SocServerHost
        Credential          = Unprotect-SDSecret -UserName:$config.SocqUser -Content:$config.SocqPass
        DefaultHostName     = $config.SocqHostname
        DefaultServiceName  = $config.SocqService
    }
    Initialize-SDSOCQ @params

    foreach ( $environment in $config.Environments )
    {
        $params =
        @{
            Credential = Unprotect-SDSecret -UserName:$environment.ClientId -Content:$environment.ClientSecret
            Method = 'GET'
            Uri = '/api/v2/organizations/me'
        }
        $org = Invoke-SDGCRestMethod @params
        $params = $null

        if ( $org.name -ne $environment.GenesysOrg )
        {
            $msg = 'Connected to Genesys organization "{0}" but expected "{1}"' -f
                $org.name,
                $environment.GenesysOrg
            throw $msg
        }

        $params =
        @{
            Instance = 'localhost'
            # Instance = 'pcc-mssql-f1.du.deakin.edu.au'
            # Credential = Unprotect-SDSecret -Content:() -UserName:'tableau_ro'
            DefaultDatabase = $environment.Database
        }
        $dbCon = Connect-SDMSSQLDatabase @params
        $sqlAuditInsertCommand = $null
        if ( $dbCon.Database -ne $environment.Database )
        {
            $msg = 'Connected to database "{0}" but expected "{1}"' -f
                $dbCon.Database,
                $environment.Database
            throw $msg
        }

        $isProduction = -not ($org.name -match 'Dev' -and $dbCon.Database -match 'Dev')
        Write-Output (
            'Connected to Genesys organization "{0}", database "{1}", production: {2}' -f
                $org.name,
                $dbCon.Database,
                $isProduction
        ) | Out-Default

        #region Disconnect user sessions that have been idle for specified duration.
        $countLoggedOut = 0
        $countSkippedUsers = 0
        $results = @(Invoke-SDMSSQLQuery -Query:$sqlInactiveUsers)
        :nextUser foreach ( $user in $results )
        {
            # Double check the users status before logging out to ensure their
            # state has not changed since the database was last updated.
            $params =
            @{
                Method = 'GET'
                Uri = '/api/v2/users/{0}?expand=routingStatus%2Cpresence' -f $user.userid
            }
            $userStatus = Invoke-SDGCRestMethod @params
            $presTimeSec = [int]([DateTime]::Now - $userStatus.presence.modifiedDate.ToLocalTime()).TotalSeconds
            $presStatus = $userStatus.presence.presenceDefinition.systemPresence
            $routeStatus = $userStatus.routingStatus.status
            if
            (
                $presStatus -ne $user.systempresence -or
                $presTimeSec -lt $user.AgentTime -or
                $routeStatus -ne $user.routingstatus
            )
            {
                $countSkippedUsers++
                $msg = 'date="{0}",' -f [DateTime]::Now.ToString('s')
                $msg += 'action="SkipUser",user="{0}",actualpresence="{1}",databasepresence="{2}",' -f
                    $user.agentname,
                    $presStatus,
                    $user.systempresence
                $msg += 'actualpresencetime="{0}",databasepresencetime="{1}"' -f
                    $presTimeSec,
                    $user.AgentTime
                $msg += 'actualroutestatus="{0}",databaseroutestatus="{1}"' -f
                    $routeStatus,
                    $user.routingstatus
                $msg | Out-Default
                continue nextUser
            }

            # Check if the user has had any activity in the last 2 hours.
            $lookbackTime = [TimeSpan]::FromHours(2)
            $servicesToCheck = @(
                'Architect'
                'WorkforceManagement'
                # 'ResponseManagement'
                # 'Telephony'
                # 'Outbound'
            )
            foreach ( $serviceName in $servicesToCheck )
            {
                $dateRangeStr = '{0}Z/{1}Z' -f
                    ([DateTime]::UtcNow - $lookbackTime).ToString('s'),
                    [DateTime]::UtcNow.ToString('s')
                $body = @{
                    interval = $dateRangeStr
                    serviceName = $serviceName
                    filters =
                    @(
                        @{
                            property = "UserId"
                            value = $user.userid
                        }
                    )
                    sort = @(
                        @{
                            name = "Timestamp"
                            sortOrder = "desc"
                        }
                    )
                }
                $body = $body | ConvertTo-Json
                $activities = Invoke-SDGCRestMethod -Method:'POST' -Uri:'/api/v2/audits/query/realtime' -Body:$body
                if ( @(Get-Member -InputObject:$activities -Name:'entities').Count -ge 1 )
                {
                    $activity = $activities.entities | Select-Object -First:1
                    if ( $null -ne $activity )
                    {
                        $msg = 'date="{0}",' -f [DateTime]::Now.ToString('s')
                        $msg += 'action="SkipUser",user="{0}",' -f $user.agentname
                        $msg += 'eventDate="{0}",serviceName="{1}",actionTaken="{2}",eventType="{3}"' -f
                            $activity.eventDate.ToString('s'),
                            $activity.serviceName,
                            $activity.action,
                            $activity.entityType
                        $msg | Out-Default
                        continue nextUser
                    }
                }
            }

            $nowTime = [DateTime]::Now
            $msg = 'date="{0}",' -f $nowTime.ToString('s')
            $msg += 'action="Logout",user="{0}",department="{1}",presence="{2}",routingstatus="{3}",' -f
                $user.agentname,            # 0
                $user.department,           # 1
                $user.systempresence,       # 2
                $user.routingstatus         # 3
            $msg += 'agentstatus="{0}",adherencestate="{1}",agenttime="{2:N1}",userid="{3}"' -f
                $user.AgentStatus,          # 0
                $user.adherencestate,       # 1
                ($user.AgentTime / 60 / 60),# 2
                $user.userid                # 3
            $msg | Out-Default

            $shouldProcess = $psCmdlet.ShouldProcess($user.agentname, 'Logout')
            if ( $shouldProcess )
            {
                Invoke-SDGCRestMethod -Method 'DELETE' -Uri:('/api/v2/apps/users/{0}/logout' -f $user.userid) | Out-Null
                if ( $null -eq $sqlAuditInsertCommand )
                {
                    $params =
                    @{
                        Query           = $sqlAuditInsertQuery
                        BindParameter   =
                        @{
                            'p_time'            = [DateTime]::MinValue
                            'p_organisation'    = ''
                            'p_action'          = ''
                            'p_object'          = ''
                            'p_duration'        = 0
                        }
                    }
                    $sqlAuditInsertCommand = Initialize-SDMSSQLCommand @params
                }
                $params =
                @{
                    Command         = $sqlAuditInsertCommand
                    AsNonQuery      = $true
                    BindParameter   =
                    @{
                        'p_time'            = $nowTime
                        'p_organisation'    = $org.id
                        'p_action'          = 'Logout'
                        'p_object'          = $user.userid
                        'p_duration'        = $user.AgentTime
                    }
                }
                $res = Invoke-SDMSSQLCommand @params
                if ( $res -ne 1 )
                {
                    throw (
                        'Failed to update audit table, {0} rows modified' -f
                            $res
                    )
                }
                $countLoggedOut++
            }
        }
        if ( $results.Count -gt 1 -and $countSkippedUsers / $results.Count -gt 0.5 )
        {
            $msg = '{0:P1} ({1}/{2}) of inactive users have changed state since the last database update ({3}). ' -f
                ($countSkippedUsers / $results.Count),  # 0
                $countSkippedUsers,                     # 1
                $results.Count,                         # 2
                $environment.Database                   # 3
            $msg += 'Is the Genesys realtime BI adapter running?'
            Write-Warning $msg
            Add-SDSOCQServiceResult -Status:'Warning' -Output:$msg
        }
        #endregion

        #region Disconnect interactions
        $countDisconnected = 0
        $countSkippedConversations = 0
        $results = @(Invoke-SDMSSQLQuery -Query:$sqlConversationsToDisconnect)
        if ( $results.Count -gt $environment.DisconnectLimit )
        {
            $msg = '{0} conversations to be disconnected exceeds maximum configured ({1}) for database "{2}"' -f
                $results.Count,                 # 0
                $environment.DisconnectLimit,   # 1
                $environment.Database           # 2
            Write-Warning $msg
            Add-SDSOCQServiceResult -Status:'Warning' -Output:$msg
            $results = @()
        }
        :nextConv foreach ( $conv in $results )
        {
            # Double check the interaction before disconnecting out to ensure
            # the state has not changed since the database was last updated.
            $params =
            @{
                Method = 'GET'
                Uri = '/api/v2/conversations/{0}' -f $conv.conversationid
            }
            $convStatus = Invoke-SDGCRestMethod @params
            $hasEnded = @(Get-Member -InputObject:$convStatus -Name:'endTime').Count -gt 0
            if ( $hasEnded )
            {
                $startTime = 'null'
                $hasStarted = @(Get-Member -InputObject:$convStatus -Name:'startTime').Count -gt 0 -and
                    $null -ne $convStatus.startTime
                if ( $hasStarted )
                {
                    $startTime = $convStatus.startTime.ToLocalTime().ToString('s')
                }
                $countSkippedConversations++
                $msg = 'date="{0}",' -f [DateTime]::Now.ToString('s')
                $msg += 'action="SkipEndedConversation",conversation="{0}",startTime="{1}"' -f
                    $conv.conversationid,
                    $startTime
                $msg | Out-Default
                continue nextConv
            }

            $nowTime = [DateTime]::Now
            $msg = 'date="{0}",' -f $nowTime.ToString('s')
            $msg += 'action="Disconnect",conversation="{0}",direction="{1}",startTime="{2}",' -f
                $conv.conversationid,                               # 0
                $conv.direction,                                    # 1
                $convStatus.startTime.ToLocalTime().ToString('s')   # 2
            $msg += 'media="{0}",status="{1}"' -f
                $conv.media,
                $conv.statusSecs
            $msg | Out-Default

            $shouldProcess = $psCmdlet.ShouldProcess($conv.conversationid, 'Disconnect conversation')
            if ( $shouldProcess )
            {
                $params =
                @{
                    Method = 'POST'
                    Uri = '/api/v2/conversations/{0}/disconnect' -f $conv.conversationid
                }
                if ( $shouldProcess )
                {
                    Invoke-SDGCRestMethod @params | Out-Null
                    if ( $null -eq $sqlAuditInsertCommand )
                    {
                        $params =
                        @{
                            Query           = $sqlAuditInsertQuery
                            BindParameter   =
                            @{
                                'p_time'            = [DateTime]::MinValue
                                'p_organisation'    = ''
                                'p_action'          = ''
                                'p_object'          = ''
                                'p_duration'        = 0
                            }
                        }
                        $sqlAuditInsertCommand = Initialize-SDMSSQLCommand @params
                    }
                    $params =
                    @{
                        Command         = $sqlAuditInsertCommand
                        AsNonQuery      = $true
                        BindParameter   =
                        @{
                            'p_time'            = $nowTime
                            'p_organisation'    = $org.id
                            'p_action'          = 'Disconnect'
                            'p_object'          = $conv.conversationid
                            'p_duration'        = $conv.statusSecs
                        }
                    }
                    $res = Invoke-SDMSSQLCommand @params
                    if ( $res -ne 1 )
                    {
                        throw (
                            'Failed to update audit table, {0} rows modified' -f
                                $res
                        )
                    }
                    $countDisconnected++
                }
            }
        }
        if ( $results.Count -gt 1 -and $countSkippedConversations / $results.Count -gt 0.5 )
        {
            $msg = '{0:P1} ({1}/{2}) of conversations have changed state since the last database update ({3}). ' -f
                ($countSkippedConversations / $results.Count),  # 0
                $countSkippedConversations,                     # 1
                $results.Count,                                 # 2
                $environment.Database                           # 3
            $msg += 'Is the Genesys realtime BI adapter running?'
            Write-Warning $msg
            Add-SDSOCQServiceResult -Status:'Warning' -Output:$msg
        }
        #endregion

        $msg = 'Logged out {0} users (skipped {1}), disconnected {2} interactions (skipped {3})' -f
            $countLoggedOut,
            $countSkippedUsers,
            $countDisconnected,
            $countSkippedConversations
        if ( $isProduction )
        {
            Add-SDSOCQServiceResult -Status:'OK' -Output:$msg
        }
        else
        {
            $msg | Out-Default
            $msg = 'No actions processed on subproduction environment '
            if ( $countLoggedOut + $countDisconnected -gt 0 )
            {
                $msg = 'Processed actions on subproduction environment '
            }
            $msg += $environment.GenesysOrg
            Add-SDSOCQServiceResult -Status:'OK' -Output:$msg
        }
    }
}
catch
{
    $msg = '{0} at {1}:{2}' -f $_.Exception.Message, $_.InvocationInfo.ScriptName, $_.InvocationInfo.ScriptLineNumber
    Write-Warning $msg
    Get-PSCallStack | Select-Object -ExpandProperty:'Location' | Out-Default
    Add-SDSOCQServiceResult -Status:'Unknown' -Output:$msg
}
finally
{
    Submit-SDSOCQServiceResult
    if ( $null -ne $config.LogDirectory )
    {
        Stop-Transcript
    }
}

# cSpell:ignore Conv conversationid Cpresence CSIOT mssql pres queueid