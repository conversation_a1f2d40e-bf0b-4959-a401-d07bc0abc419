#Requires -Version 7
$ErrorActionPreference = 'Stop'
$ProgressPreference = 'SilentlyContinue'

'Setting up dependencies...' | Out-Default

$requiredModules =
@(
    'PSScriptAnalyzer'
)
$installedModules = @(Get-Module -ListAvailable).Name
foreach ( $module in $requiredModules )
{
    if ( $module -NotIn $installedModules )
    {
        Install-Module -Force -Name:$module
    }
}

'Running ScriptAnalyzer...' | Out-Default
$params =
@{
    Path = '.'
    Severity = 'Error', 'Warning'
    Recurse = $true
    Settings = './PSScriptAnalyzerSettings.psd1'
}
$results = @(Invoke-ScriptAnalyzer @params)
"Found {0} ScriptAnalyzer issues" -f $results.Count
if ( $results.Count -gt 0 )
{
    $results
    exit 1
}
