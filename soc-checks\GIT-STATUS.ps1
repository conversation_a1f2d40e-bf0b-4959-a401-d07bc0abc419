<#
.SYNOPSIS

Check the status of Git repositories.

.DESCRIPTION

Reports the status of all Git repositories under the specified file system paths.

# cSpell:ignore SDSOCQ Creds Socq
#>
[CmdletBinding(SupportsShouldProcess = $true)]
Param
(
    [Parameter()]
    [IO.FileInfo[]]
    # The file system directories to scan for Git repositories.
    $Path = ('C:\deakin', 'C:\scripts', "$env:OneDriveCommercial\repos\deakin"),

    [Parameter()]
    [string]
    # Only check repositories whose config file content matches this regular expression.
    $ConfigContains = '^\s*url\s*=.+gitlab.its.deakin.edu.au[/:]esolutions/service-desk/',

    [Parameter()]
    [TimeSpan]
    # The frequency to fetch the repository.
    $FetchInterval = [TimeSpan]::FromHours(7),

    [Parameter()]
    [switch]
    # Perform a Git fetch immediately.
    $Force,

    [Parameter()]
    [switch]
    # Report the results to SOC.
    $ReportStatus,
    [Parameter()]

    [switch]
    # Attempt to remediate discovered issues
    $FixIfPossible
)

#Requires -Version 5
$ErrorActionPreference = 'Stop'
Set-StrictMode -Version:5
Import-Module -Name:"${PSScriptRoot}\..\modules\SDSecretManagement" -Verbose:$false
Import-Module -Name:"${PSScriptRoot}\..\modules\SDSOCQ" -Verbose:$false

# Load configuration
$config =
@{
    SocHostname = $null
    AutoFixRepos = @()
}
if ( Test-Path -LiteralPath:"${PSScriptRoot}\GIT-STATUS.psd1" )
{
    $config = Import-LocalizedData
    if ( $config.ContainsKey('AutoFixRepos') -eq $false )
    {
        $config.AutoFixRepos = @()
    }
}
if ( $ReportStatus )
{
    if ( [string]::IsNullOrEmpty($config.SocHostname) )
    {
        $ReportStatus = $false
    }
    else
    {
        $socCreds = Unprotect-SDSecret -Content:$config.SocqPass -UserName:$config.SocqUser
        Initialize-SDSOCQ -Credential:$socCreds -DefaultHostName:$config.SocHostname
    }
}

$repoStatuses = @()
$needPop = $false

try
{
    Write-Output ('Searching for repositories under "' + ($Path -join '", "') + '"...')
    $repos =
    @(
        $Path |
            Where-Object { Test-Path -Path $_ } |
            Get-ChildItem -Recurse -Force -File -Filter:'config' -ErrorAction:'SilentlyContinue' |
            Where-Object PSParentPath -Match '\\\.git$' |
            Select-String -Pattern:$ConfigContains |
            Select-Object -ExpandProperty Path -Unique |
            ForEach-Object { Split-Path -LiteralPath:(Split-Path -LiteralPath:$_) }
    )

    Write-Output (
        '{0} Git repositories found' -f $repos.Count
    )

    :nextRepo foreach ( $repoBasePath in $repos )
    {
        # Make sure the last exit code is clear for each repository.
        $LASTEXITCODE = 0

        $repoName = Split-Path -Leaf -Path:$repoBasePath
        Write-Output ('Checking repository {0} ({1})...' -f $repoName, $repoBasePath)

        Push-Location -LiteralPath:$repoBasePath
        $needPop = $true

        $shouldFix = $FixIfPossible -or $config.AutoFixRepos -contains $repoName
        $status =
        @{
            'Name'          = $repoName
            'Path'          = $repoBasePath
            'Branch'        = $null
            'LastFetch'     = [DateTime]::MinValue
            'StatusDetail'  = $null
            'StatusSummary' = $null
            'Submodules'    = @()
            'Warnings'      = @()
        }

        #region Check last fetch time, and fetch if required.
        if ( Test-Path -LiteralPath '.git/FETCH_HEAD' )
        {
            $status.LastFetch = Get-Item -LiteralPath:'.git/FETCH_HEAD' |
                Sort-Object -Property:'LastWriteTime' |
                Select-Object -ExpandProperty:'LastWriteTime'
        }
        $shouldFetch = [DateTime]::Now - $status.LastFetch -gt $FetchInterval
        if ( $shouldFetch )
        {
            $shouldFetch = $psCmdlet.ShouldProcess($status.Path, 'git fetch')
        }
        if ( $Force )
        {
            $shouldFetch = $true
        }
        if ( $shouldFetch )
        {
            git fetch --recurse-submodules
            if ( $LASTEXITCODE -ne 0 )
            {
                Write-Warning "Git fetch error with exit code ${LASTEXITCODE}."
            }

            if ( Test-Path -LiteralPath '.git/FETCH_HEAD' )
            {
                $status.LastFetch = Get-Item -LiteralPath:'.git/FETCH_HEAD' |
                    Sort-Object -Property:'LastWriteTime' |
                    Select-Object -ExpandProperty:'LastWriteTime'
            }

            $shouldFetch = [DateTime]::Now - $status.LastFetch -gt $FetchInterval
            if ( $shouldFetch )
            {
                $status.Warnings += 'Last fetch exceeds threshold'
            }
        }
        #endregion

        #region Check branch
        $status.Branch = git branch --show-current --no-color
        if ( $LASTEXITCODE -ne 0 )
        {
            $status.Warnings += "Git branch exited with exit code ${LASTEXITCODE}."
        }
        if ( [string]::IsNullOrEmpty($status.Branch) )
        {
            $status.Branch = 'Detached head'
            $status.Warnings += $status.Branch
        }
        #endregion

        #region Check submodules
        $status.Submodules = @(git submodule status --recursive)
        if ( $LASTEXITCODE -ne 0 )
        {
            $status.Warnings += "Git submodule exited with exit code ${LASTEXITCODE}."
        }
        foreach ( $submodule in $status.Submodules )
        {
            if ( $submodule -match '^(.)(.{40}) (.+) \(' )
            {
                $initState = $matches[1]
                # $sha = $matches[2]
                $name = $matches[3]
                switch ( $initState )
                {
                    '-'
                    {
                        $status.Warnings += 'Submodule not initialized: ' + $name
                        $fixCmd = 'submodule init ' + $name
                        if ( $shouldFix -and $psCmdlet.ShouldProcess($status.Path, $fixCmd) )
                        {
                            git submodule init $name
                        }
                    }
                    '+'
                    {
                        $status.Warnings += 'Submodule does not match index: ' + $name
                        $fixCmd = 'submodule update ' + $name
                        if ( $shouldFix -and $psCmdlet.ShouldProcess($status.Path, $fixCmd) )
                        {
                            git submodule update $name
                        }
                    }
                    'U'
                    {
                        $status.Warnings += 'Submodule has merge conflicts: ' + $name
                    }
                    ' ' {} # This is good.
                    default
                    {
                        $status.Warnings += 'Submodule has unknown state: ' + $name
                    }
                }
            }
            else
            {
                $status.Warnings += ('Unexpected submodule output "{0}"' -f $submodule)
            }
        }
        #endregion

        #region merged, modified, untracked
        $status.StatusDetail =
        @(
            git status --porcelain=v2 --ignore-submodules=none --ahead-behind --renames -b --untracked-files=normal
        )
        if ( $LASTEXITCODE -ne 0 )
        {
            $status.Warnings += "Git status exited with exit code ${LASTEXITCODE}."
        }
        $countModified = 0
        $countUnmerged = 0
        $countUntracked = 0
        foreach ( $line in $status.StatusDetail )
        {
            switch -Regex ( $line )
            {
                '^# branch.ab \+(\d+) -(\d+)'
                {
                    $aheadBy = $matches[1]
                    $behindBy = $matches[2]
                    if ( $aheadBy -gt 0 -or $behindBy -gt 0 )
                    {
                        $status.Warnings += 'Behind by {0}, ahead by {1}' -f $behindBy, $aheadBy
                        if ( $shouldFix )
                        {
                            if ( $behindBy -gt 0 -and $psCmdlet.ShouldProcess($status.Path, 'git pull') )
                            {
                                git pull
                            }
                            if ( $aheadBy -gt 0 -and $psCmdlet.ShouldProcess($status.Path, 'git push') )
                            {
                                git push
                            }
                        }
                    }
                }
                '^[12] '
                {
                    $countModified++
                }
                '^u '
                {
                    $countUnmerged++
                }
                '^\? '
                {
                    $countUntracked++
                }
                '^! ' {} # Ignored files
                '^# ' {} # Ignore commented lines we don't understand as per git documentation
                default
                {
                    $status.Warnings += "Unrecognised status line: '$line'"
                }
            }
        }
        if ( $countModified -gt 0 -or $countUnmerged -gt 0 -or $countUntracked -gt 0 )
        {
            $msg = '{0} modified, {1} unmerged, {2} untracked files' -f
                $countModified,
                $countUnmerged,
                $countUntracked

            $status.Warnings += $msg
        }
        #endregion

        #region summarise warnings
        $warnMsgs = 'up to date, working tree clean'
        if ( $status.Warnings.Count -gt 0 )
        {
            $warnMsgs = $status.Warnings -join ', '
        }
        $status.StatusSummary = $warnMsgs
        #endregion

        Pop-Location
        $needPop = $false
        $repoStatuses += [PSCustomObject]$status
    }
}
catch
{
    $output = '{0} at {1}:{2}' -f
        $_.Exception.Message,
        $_.InvocationInfo.ScriptName,
        $_.InvocationInfo.ScriptLineNumber

    if ( $ReportStatus )
    {
        Add-SDSOCQServiceResult -ServiceName:'GIT-STATUS' -Status:'Warning' -Output:$output
    }
    Write-Warning $output
}
finally
{
    if ( $needPop )
    {
        Pop-Location
    }
    $repoStatuses = $repoStatuses | Sort-Object -Property:{ $_.Warnings.Count }
    Write-Output ''
    Write-Output ''
    foreach ( $repo in $repoStatuses )
    {
        $output = '{0} [{1}]: {2}' -f $repo.Name, $repo.Branch, $repo.StatusSummary
        $level = 'Ok'
        if ( $repo.Warnings.Count -gt 0 )
        {
            $level = 'Warning'
            $output += ' (Full path: {0})' -f $repo.Path
            Write-Warning $output
        }
        elseif ( -not $ReportStatus )
        {
            Write-Output $output
        }
        if ( $ReportStatus )
        {
            Add-SDSOCQServiceResult -ServiceName:'GIT-STATUS' -Status:$level -Output:$output
        }
    }
    if ( $ReportStatus )
    {
        Submit-SDSOCQServiceResult
    }
}
