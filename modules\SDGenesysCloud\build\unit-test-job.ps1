[CmdletBinding(SupportsShouldProcess = $false)]
[System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("PSUseCompatibleCommands", "")]
Param()

#Requires -Version 7
$ErrorActionPreference = 'Stop'
$ProgressPreference = 'SilentlyContinue'

'Setting up dependencies...' | Out-Default

$requiredModules =
@(
    'Pester'
)
$installedModules = @(Get-Module -ListAvailable).Name
foreach ( $module in $requiredModules )
{
    if ( $module -NotIn $installedModules )
    {
        Install-Module -Force -Name:$module
    }
}

'Running Pester...' | Out-Default
Import-Module -Name:'Pester' -MinimumVersion:'5.0'
$config = [PesterConfiguration]::Default
$config.TestResult.OutputPath = Join-Path -Path:$PSScriptRoot -ChildPath:'unit-test-job.xml'
$config.TestResult.OutputFormat = 'JUnitXml'
$config.TestResult.Enabled = $true
$config.Filter.ExcludeTag = 'unstable'
$config.Run.PassThru = $true
$config.Output = @{ 'Verbosity' = 'Detailed' }
$results = Invoke-Pester -Configuration:$config
if ( $results.FailedCount -gt 0 )
{
    exit 1
}
