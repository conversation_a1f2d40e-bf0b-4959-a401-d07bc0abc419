# Various helper cmdlets to interact with Genesys Cloud.

#region Exported functions
#
#

function Invoke-SDGCRestMethod
{
    <#
    .SYNOPSIS

    Wrapper around Invoke-WebRequest to invoke Genesys Cloud API calls

    .DESCRIPTION

    Handles paging, rate limiting, errors, etc from Genesys Cloud API.

    Runs on PowerShell v4, but has limitations. Best results on PowerShell 6+.

    .EXAMPLE

    (Invoke-SDGCRestMethod -Method:'GET' -Uri:'/api/v2/organizations/me').id
    ba4b1d40-d0d6-4e5e-98f2-d1a19d075a86

    .LINK

    https://developer.mypurecloud.com/

    #>
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'Medium', PositionalBinding = $false)]
    Param
    (
        # The API method to invoke. GET requests won't trigger WhatIf or Confirm
        [Parameter(Position = 0)]
        [ValidateSet('Get', 'Head', 'Post', 'Put', 'Delete', 'Trace', 'Options', 'Merge', 'Patch')]
        [string]
        $Method = 'GET',

        # The full or relative URI of the API.
        # i.e. /api/v2/organizations/me or https://api.mypurecloud.com.au/api/v2/organizations/me
        [Parameter(Position = 1)]
        [ValidateNotNullOrEmpty()]
        [string]
        $Uri,

        # The API request body
        [Parameter()]
        [Object]
        $Body = $null,

        # The maximum time to spend attempting to make the request, including all
        # retry attempts, rate limiting, paging, etc.
        [Parameter()]
        [ValidateNotNull()]
        [TimeSpan]
        $Timeout = [TimeSpan]::FromMinutes(10),

        # If the response contains a page number, then attempt to retrieve the
        # next page up to this limit.
        [Parameter()]
        [ValidateNotNull()]
        [int]
        $MaxPages = 5000,

        # OAUTH API credential Client ID/Client Secret
        [Parameter()]
        [Management.Automation.Credential()]
        [Management.Automation.PSCredential]
        $Credential = [Management.Automation.PSCredential]::Empty
    )

    Trap
    {
        throw $_
    }

    # Retry on specific HTTP status codes:
    $maxRetryAttempts = 2
    $sleepBetweenUntilLimitAttempts = [TimeSpan]::FromSeconds(5)
    $sleepBetweenUntilTimeoutAttempts = [TimeSpan]::FromSeconds(15)
    $retryHttpCodes =
    @{
        # Keep trying these HTTP error codes until $Timeout is reached.
        'UntilTimeout' =
        @(
            429     # Too many requests
        )
        # Retry $maxRetryAttempts times
        'UntilRetryLimit' =
        @(
            504     # Gateway Timeout
        )
    }

    # $apiParams stores the parameters to Invoke-RestMethod and Invoke-WebRequest
    # and is used in the exception handler. Make sure it is always defined.
    $apiParams = @{ 'Method' = $Method; 'Uri' = $Uri }
    # $apiParamsCommon is always also passed to Invoke-RestMethod and
    # Invoke-WebRequest, but not used in exception handler.
    $apiParamsCommon =
    @{
        'TimeoutSec' = 180
    }
    # Handle some backwards compatibility issues.
    if ( $PSVersionTable.PSVersion -lt [version]'6.0' )
    {
        if ( $null -ne $env:HTTPS_PROXY )
        {
            $apiParamsCommon['Proxy'] = $env:HTTPS_PROXY
        }
        elseif ( $null -ne $env:ALL_PROXY )
        {
            $apiParamsCommon['Proxy'] = $env:ALL_PROXY
        }
    }
    try
    {
        #region obtain authorization token
        $clientId = if ( $null -eq $Credential ) { $null } else { $Credential.UserName }
        if ( [string]::IsNullOrWhiteSpace($clientId) )
        {
            if ( $null -eq $script:GCLastClientId )
            {
                $Credential = Get-Credential -Message:'Genesys Cloud API OAUTH credential'
                $clientId = $Credential.UserName
            }
            else
            {
                $clientId = $script:GCLastClientId
                $Credential = $script:GCTokens[$clientId].Credential
            }
        }
        if
        (
            $script:GCTokens.ContainsKey($clientId) -eq $false -or
            $script:GCTokens[$clientId] -IsNot [Hashtable] -or
            $script:GCTokens[$clientId].ContainsKey('Expires') -eq $false -or
            $script:GCTokens[$clientId]['Expires'] -lt [DateTime]::Now
        )
        {
            if
            (
                $null -eq $Credential -or
                $Credential -eq [Management.Automation.PSCredential]::Empty -or
                [string]::IsNullOrWhiteSpace($Credential.UserName)
            )
            {
                throw 'Credentials required'
            }
            $login = '{0}:{1}' -f
                $Credential.Username,
                $Credential.GetNetworkCredential().Password
            $auth = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($login))
            $apiParams =
            @{
                'Method'        = 'POST'
                'Uri'           = "https://login.${GCBaseUrl}/oauth/token"
                'Body'          = @{ 'grant_type' = 'client_credentials' }
                'Headers'       = @{ 'Authorization' = "Basic ${auth}" }
                'ContentType'   = 'application/x-www-form-urlencoded'
            }
            Write-Verbose ('Logging into Genesys Cloud API as {0}...' -f $Credential.UserName)
            Write-Verbose (Format-IntWebRequestAction $apiParams)
            $response = Invoke-WebRequest @apiParams @apiParamsCommon
            if ( $null -eq $response -or $null -eq $response.Content )
            {
                throw ('Attempt to log into Genesys Cloud API returned NULL')
            }
            $responseContent = $response.Content | ConvertFrom-Json
            $script:GCTokens[$clientId] = @{
                    'Credential'    = $Credential
                    'Expires'       = [DateTime]::Now.AddSeconds($responseContent.expires_in).AddMinutes(-5)
                    'Token'         = $responseContent.access_token
                    'TokenType'     = $responseContent.token_type
            }
        }
        $script:GCLastClientId = $clientId
        $authHeader = @{
            'Authorization' = $script:GCTokens[$clientId]['TokenType'] + ' ' + $script:GCTokens[$clientId]['Token']
            'Content-Type' = 'application/json'
        }
        #endregion

        if ( $Uri -notmatch '^https://' )
        {
            $Uri = 'https://' + $GCApiUrl + $Uri
        }

        # Make an assumption that GET operations are safe.
        $msg = Format-IntWebRequestAction -Parameters:$apiParams
        if ( $Method -ne 'GET' -and $psCmdlet.ShouldProcess($msg) -eq $false )
        {
            return
        }

        $pageCounter = 0
        $timer = [System.Diagnostics.Stopwatch]::StartNew()
        :nextPage do
        {
            $apiParams =
            @{
                'Method'        = $Method
                'Uri'           = $Uri
                'Headers'       = $authHeader
            }
            if ( $null -ne $Body )
            {
                $apiParams['Body'] = $Body
            }
            :nextAttempt for ( $attempt = 1; $attempt -le $maxRetryAttempts; $attempt++ )
            {
                $httpCode = $null
                $response = $null
                $msg = Format-IntWebRequestAction -Parameters:$apiParams
                Write-Verbose $msg
                try
                {
                    $response = Invoke-WebRequest @apiParams @apiParamsCommon
                }
                catch
                {
                    # Depending on the version of PowerShell and .NET differing
                    # exceptions will be returned.
                    # System.Web.Http.HttpResponseException:
                    #   https://docs.microsoft.com/en-us/dotnet/api/system.web.http.httpresponseexception
                    # System.Net.WebException:
                    #   https://docs.microsoft.com/en-us/dotnet/api/system.net.webexception

                    $exceptionType = $_.Exception.GetType().ToString()
                    $ok = $false
                    if ( $exceptionType -match '(HttpResponse|Web)Exception' )
                    {
                        $response = $_.Exception.Response
                        $httpCode = $null
                        if ( $null -ne $response )
                        {
                            $httpCode = [int]$response.StatusCode
                            if ( $retryHttpCodes.UntilTimeout -contains $httpCode.ToString() )
                            {
                                # Keep trying these HTTP error codes until $Timeout is reached.
                                # Can't handle them in the exception handler due to needing
                                # to continue to a label, so set a flag and deal with them
                                # outside the exception handler.
                                $ok = $true
                            }
                            elseif ( $retryHttpCodes.UntilRetryLimit -contains $httpCode.ToString() )
                            {
                                # Retry $maxRetryAttempts times
                                if ( $attempt -lt $maxRetryAttempts )
                                {
                                    Write-Warning (
                                        'Genesys Cloud API HTTP {0} [Retry {1}/{2}] {3} for request:' -f
                                            $httpCode,
                                            $attempt,
                                            $maxRetryAttempts,
                                            $_.Exception.Message
                                    )
                                    Write-Warning $msg
                                    Write-Verbose ('Sleeping {0}...' -f $sleepBetweenUntilLimitAttempts)
                                    Start-Sleep -Seconds:$sleepBetweenUntilLimitAttempts.TotalSeconds
                                    continue nextAttempt
                                }
                            }
                        }
                    }
                    if ( -not $ok )
                    {
                        throw $_
                    }
                }
                break
            }
            if ( $null -eq $response )
            {
                throw ('Genesys Cloud API call to {0} returned NULL' -f $apiParams.Uri)
            }
            $httpCode = [int]$response.StatusCode
            if ( $retryHttpCodes.UntilTimeout -contains $httpCode.ToString() )
            {
                Write-Warning (
                    'Genesys Cloud API HTTP {0} for request:' -f
                        $httpCode
                )
                Write-Warning $msg
                Write-Verbose ('Sleeping {0}...' -f $sleepBetweenUntilTimeoutAttempts)
                Start-Sleep -Seconds:$sleepBetweenUntilTimeoutAttempts.TotalSeconds
                continue nextPage
            }
            if ( $httpCode -lt 200 -or $httpCode -ge 300 )
            {
                throw (
                    'Genesys Cloud API call to {0} returned HTTP {1}' -f
                        $apiParams.Uri,
                        $httpCode
                )
            }
            $pageCounter++
            $responseData = $response.Content | ConvertFrom-Json
            if ( $null -eq $responseData )
            {
                return
            }
            $responseData

            # Deal with paging
            if ( Test-IntObjectContainsProperty -InputObject:$responseData -PropertyName:'pageCount' )
            {
                if
                (
                    $responseData.pageNumber -ge $responseData.pageCount -or
                    $pageCounter -ge $MaxPages
                )
                {
                    break
                }
                if ( Test-IntObjectContainsProperty -InputObject:$responseData -PropertyName:'nextUri' )
                {
                    $uriObj = [Uri]$Uri
                    $Uri = '{0}://{1}{2}' -f
                        $uriObj.Scheme,
                        $uriObj.Authority,
                        $responseData.nextUri
                }
                else
                {
                    if ( $null -eq $Body )
                    {
                        $Body = @{}
                    }
                    if ( $Body -is [string] )
                    {
                        $Body = ConvertFrom-Json -InputObject:$Body
                        if ( -not (Test-IntObjectContainsProperty -InputObject:$Body -Property:'pageNumber') )
                        {
                            Add-Member -InputObject:$Body -MemberType:'NoteProperty' -Name:'pageNumber' -Value:0
                        }
                        $Body.pageNumber = $responseData.pageNumber + 1
                        $Body = ConvertTo-Json -InputObject:$Body -Depth:100
                    }
                    elseif ( $Body -is [PSCustomObject] )
                    {
                        if ( -not (Test-IntObjectContainsProperty -InputObject:$Body -Property:'pageNumber') )
                        {
                            Add-Member -InputObject:$Body -MemberType:'NoteProperty' -Name:'pageNumber' -Value:0
                        }
                        $Body.pageNumber = $responseData.pageNumber + 1
                    }
                    elseif ( $Body -is [Hashtable] )
                    {
                        $Body['pageNumber'] = $responseData.pageNumber + 1
                    }
                    else
                    {
                        throw ('Need to set the page but the body is an unknown type {0}' -f $Body.GetType().ToString())
                    }
                }
            }
            else
            {
                break
            }

            if ( $timer.Elapsed -gt $Timeout )
            {
                throw ('Genesys Cloud API call to {0} timed out' -f $Uri)
            }
            if ( $pageCounter -ge $MaxPages )
            {
                throw ('Genesys Cloud call to {0} took more than {1} calls' -f $Uri, $MaxPages)
            }
        }
        while ( $true )
        Write-Verbose (
            'Genesys Cloud API call to {0} completed in {1}' -f
                $Uri,
                $timer.Elapsed
        )
    }
    catch
    {
        $exceptionType = $_.Exception.GetType().ToString()
        if ( $exceptionType -match '(HttpResponse|Web)Exception' )
        {
            # Output some diagnostic information to make troubleshooting easier.
            Write-Warning ('WebException: ' + $_.Exception.Message)
            Write-Warning (Format-IntWebRequestAction $apiParams)
            if ( Test-IntObjectContainsProperty -InputObject:$_ -Property:'ErrorDetails' )
            {
                if ( $null -ne $_.ErrorDetails )
                {
                    Write-Warning ('Error details: ' + $_.ErrorDetails)
                }
            }
        }
        throw $_
    }
} # function Invoke-SDGCRestMethod


#endregion
#
#region Private helper functions
#
#
function Format-IntWebRequestAction
{
    [CmdletBinding(SupportsShouldProcess = $false)]
    [OutputType([string])]
    Param (
        [Parameter()]
        [ValidateNotNull()]
        [hashtable]
        $Parameters
    )

    $body = ''
    if ( $Parameters.ContainsKey('Body') )
    {
        $body = $Parameters.Body
        if ( $Parameters.Body -IsNot [string] )
        {
            $body = $body | ConvertTo-Json -Depth:100
        }
    }
    $result = $Parameters.Method.ToUpper() + ' ' + $Parameters.Uri
    if ( [string]::IsNullOrWhiteSpace($body) -eq $false )
    {
        $result += "`n" + $body
    }
    $result
}

function Test-IntObjectContainsProperty
{
    [CmdletBinding(SupportsShouldProcess = $false)]
    [OutputType([System.Boolean])]
    Param
    (
        [Parameter()]
        [Object]
        $InputObject,

        [ValidateNotNullOrEmpty()]
        [Parameter()]
        [string]
        $PropertyName
    )

    if ( $null -eq $InputObject )
    {
        return $false
    }

    $members =
    @(
        Get-Member -InputObject:$InputObject -MemberType:'*Property' |
            Select-Object -ExpandProperty:'Name'
    )
    return $members -contains $PropertyName
}

#endregion
#
#region Export members
#
#
#Requires -Version:5.1
Set-StrictMode -Version:5
$script:ErrorActionPreference = 'Stop'

$script:GCBaseUrl = 'mypurecloud.com.au'
$script:GCApiUrl = "api.${GCBaseUrl}"
$script:GCTokens = @{}
$script:GCLastClientId = $null

Export-ModuleMember -Function:'Invoke-SDGCRestMethod'

#endregion

# cSpell:ignore mypurecloud