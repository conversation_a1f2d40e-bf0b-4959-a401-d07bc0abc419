# SDSecretManagement PowerShell Module

Encryption and decryption of secrets.

The encryption is handled by either:
* A data encipherment certificate in the LocalMachine certificate store.
* or the Windows credential manager of the local account.

The data encipherment certificate is the best solution for servers as the
secrets are available to all users who can access the certificate.
Administrators of the computer by default can access the key material of
certificates stored in the LocalMachine store (elevation is required).

The Windows credential manager limits the availability of the secret to the
user account who generated the ciphertext of the secret, but elevation is not
required.

| Cmdlet                      | Description                                                |
|-----------------------------|------------------------------------------------------------|
| Get-SDEncryptionCertificate | Get the data encipherment certificate, if available        |
| New-SDEncryptionCertificate | Create a data encipherment certificate (optional)          |
| Protect-SDSecret            | Encrypt secrets for secure storage                         |
| Unprotect-SDSecret          | Decrypt secrets previously encrypted with Protect-SDSecret |
