{
    // Use a custom PowerShell Script Analyzer settings file for this workspace.
    // Relative paths for this setting are always relative to the workspace root dir.
    "powershell.scriptAnalysis.settingsPath": "./PSScriptAnalyzerSettings.psd1",
    "cSpell.words": [
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>K<PERSON>"
    ],
    "cSpell.enableFiletypes": [
        "powershell"
    ],
    "cSpell.allowCompoundWords": true,
    "cSpell.diagnosticLevel": "Hint"
}

