#Requires -Version 4.0
<#
.DESCRIPTION
    Creates PureCloud external contacts based off a LDAP filter.

    API documentation:
        https://developer.mypurecloud.com.au/api/rest/v2/

    API explorer:
        https://developer.mypurecloud.com.au/developer-tools/#/api-explorer

    Sys - Troubleshooting - PureCloud Contact Centre
        https://wiki.deakin.edu.au/x/cb6lBg

#>
[Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidLongLines', "")]
[CmdletBinding(SupportsShouldProcess = $true)]
Param
(
    [string]
    $OneShot
)

function Format-PureCloudPhoneNumber([string]$Number)
{
    $num = $Number
    $e164 = ''
    $ext = ''
    if ( $num -match '^\+' )
    {
        $e164 = $num -replace '\s+'
        if ( $e164 -match '^\+\d+;(\d+)$' )
        {
            $ext = [int]$matches[1]
            $num = $num -replace ';.+$'
            $num += ' ext. ' + $ext
            $e164 = $e164 -replace ';.+$'
        }
    }
    $res = @{ 'display' = $num }
    if ( [string]::IsNullOrEmpty($e164) -eq $false )
    {
        $res['e164'] = $e164
    }
    if ( [string]::IsNullOrEmpty($ext) -eq $false )
    {
        $res['extension'] = $ext
    }

    # HACK: At the moment, calling internal only (non direct in dial/private)
    # numbers on external contacts does not work in PureCloud. Setting the
    # extension and e164 correctly results in a dialling error. So for now, set
    # the 5 digit, which is invalid, but it at least shows the number so it can
    # be manually dialled (which works)
    if ( $res.ContainsKey('extension') )
    {
        $res = @{ 'display' = $res['extension'] }
    }
    $res
}

function Format-LdrString
{
    Param
    (
        [string] $InputObject
    )

    # Strip Unicode characters as PureCloud does not store them (i.e. they
    # convert to ASCII on their side, resulting in a difference every run)
    [Text.Encoding]::ASCII.GetString(
        [Text.Encoding]::Convert(
            [Text.Encoding]::UTF8,
            [Text.Encoding]::GetEncoding(
                [Text.Encoding]::ASCII.EncodingName,
                [Text.EncoderReplacementFallback]'?',
                (New-Object -TypeName:'System.Text.DecoderExceptionFallback')
            ),
            [Text.Encoding]::UTF8.GetBytes($InputObject)
        )
    )
}

function Get-LdrRequestBody([hashtable]$Body)
{
    if ( [string]::IsNullOrEmpty($Body['firstName']) )
    {
        $Body['firstName'] = '.'
    }

    if ( [string]::IsNullOrEmpty($Body['lastName']) )
    {
        $Body['lastName'] = '.'
    }

    $Body
}

$script:ErrorActionPreference = 'Stop'
Set-StrictMode -Version 4

try
{
    . 'C:\scripts\includes\Common-WindowsFunctions.ps1'
    . 'C:\scripts\soc_local\LocalCheck.ps1'

    Start-DknScriptLogging -Confirm:$false

    Initialize-DknSocAlert -HelpText:'https://wiki.deakin.edu.au/x/cb6lBg'

    Import-Module -Name:'DeakinPureCloudAPI'
    $PSDefaultParameterValues += Get-DknModuleDefaultParameterValues -ModuleName:'DeakinPureCloudAPI'


    ############################################################################
    #
    # Settings
    #
    ############################################################################

    # LDAP filter of Active Directory users
    $ldapFilter = '(&(objectCategory=person)(!userAccountControl:1.2.840.113556.1.4.803:=2)(!msExchHideFromAddressLists=TRUE)(extensionAttribute4=*)(|(telephoneNumber=*)(mobile=*)))'
    #                 ^                      ^                                              ^                                 ^                      ^
    #                 Peeps only             Account enabled                                Not hidden from the GAL           Key for sync           Has a phone number in the directory

    # Active Directory OU to search for users, has domain DN appended
    # automatically
    $adSearchBase       = 'OU=User Accounts,'

    # How many PureCloud contacts to pull back in one request, 100 seems to be
    # the max at the moment
    $pcPageSize         = 100

    # Which external contact organization is used for (and owned by the loader)
    # for contacts
    $pcOrganizationName = 'DU'

    # Maximum contacts to delete in one run
    $maxDeletionsPerRun = 5000

    # This is to prevent the same change being made every run. The number of
    # changes being actioned this run on accounts modified in the last 48 hours
    # must be below this threshold.
    $maxRapidChanges    = 25

    # List of AD attributes to retrieve from AD
    $adAttributes = @(
        'cn'
        'extensionAttribute4'
        'givenName'
        'mobile'
        'sn'
        'telephoneNumber'
        'title'
    )

    # Map AD attributes to PureCloud attributes
    $attributeMappings = @(

        # Attribute Mapping: PureCloud ID
        @{
            'AdValue'       = { $pcId }
            'PcAttribute'   = 'id'
        }

        # Attribute Mapping: Given Name
        @{
            'AdValue'       = {
                if ( [string]::IsNullOrEmpty($adUser.givenName) )
                {
                    '.'
                }
                else
                {
                    $adUser.givenName
                }
            }
            'PcAttribute'   = 'firstName'
        }

        # Attribute Mapping: Surname
        @{
            'AdValue'       = {
                if ( [string]::IsNullOrEmpty($adUser.sn) )
                {
                    '.'
                }
                else
                {
                    $adUser.sn
                }
            }
            'PcAttribute'   = 'lastName'
        }

        # Attribute Mapping: Salutation
        @{
            'AdAttribute'   = 'personalTitle'
            'PcAttribute'   = 'salutation'
        }

        # Attribute Mapping: Title
        @{
            'AdValue'       = {
                if ( [string]::IsNullOrEmpty($adUser.title) -eq $false )
                {
                    Format-LdrString -InputObject:$adUser.title.Trim()
                }
            }
            'PcAttribute'   = 'title'
        }

        # Attribute Mapping: Email
        @{
            'AdAttribute'   = 'mail'
            'PcAttribute'   = 'workEmail'
        }

        # Attribute Mapping: PureCloud Organization ID
        @{
            'AdValue'       = { $orgId }
            'PcAttribute'   = 'externalOrganization.id'
        }

        # Attribute Mapping: Account ID
        @{
            'AdValue'       = { 'ID' + $acctId }
            'PcAttribute'   = 'address.address2'
        }

        # Attribute Mapping: Work phone number
        @{
            'AdValue'       = {
                if ( $phoneWork.ContainsKey('e164') )
                {
                    $phoneWork.e164
                }
            }
            'PcAttribute'   = 'workPhone.e164'
        }

        # Attribute Mapping: Work phone display
        @{
            'AdValue'       = {
                if ( $phoneWork.ContainsKey('display') )
                {
                    $phoneWork.display
                }
            }
            'PcAttribute'   = 'workPhone.display'
            'Diff'          = {
                ($adValue -replace '\s+') -cne ($pcValue -replace '\s+')
            }
        }

        # Attribute Mapping: Work phone extension
        @{
            'AdValue'       = {
                if ( $phoneWork.ContainsKey('extension') )
                {
                    $phoneWork.extension
                }
            }
            'PcAttribute'   = 'workPhone.extension'
        }

        # Attribute Mapping: Mobile phone number
        @{
            'AdValue'       = {
                if ( $phoneMob.ContainsKey('e164') )
                {
                    $phoneMob.e164
                }
            }
            'PcAttribute'   = 'cellPhone.e164'
        }

        # Attribute Mapping: Mobile phone display
        @{
            'AdValue'       = {
                if ( $phoneMob.ContainsKey('display') )
                {
                    $phoneMob.display
                }
            }
            'PcAttribute'   = 'cellPhone.display'
            'Diff'          = {
                ($adValue -replace '\s+') -cne ($pcValue -replace '\s+')
            }
        }

        # Attribute Mapping: Mobile phone extension
        @{
            'AdValue'       = {
                if ( $phoneMob.ContainsKey('extension') )
                {
                    $phoneMob.extension
                }
            }
            'PcAttribute'   = 'cellPhone.extension'
        }
    )

    $domainDn = GetCurrentDomainDN
    $adSearchBase += $domainDn
    $isOneShot = $false
    $oneShotAccountId = $null
    $cnLdap = ''
    if ( [string]::IsNullOrEmpty($OneShot) -eq $false )
    {
        $isOneShot = $true
        $cnLdap = '(cn={0})' -f (Escape-DknLdapFilter -InputObject:$OneShot)
        $ldapFilter = $ldapFilter -replace '(\))$', "${cnLdap}`$1"
        WriteOutput (
            'Action="OneShot",Message="Running in OneShot mode",LdapFilter="{0}"' -f
            $cnLdap
        ) -Type:'Warning'
    }

    ############################################################################
    #
    # Grab all PureCloud agents, so we can exclude them from being created as
    # contacts (they are already in the directory)
    #
    ############################################################################
    $agentEmailAddresses = @(
        Invoke-PureCloudWebRequest -Call:'/api/v2/routing/queues/' -WhatIf:$false | `
        Select-Object -ExpandProperty:'entities' | `
        ForEach-Object `
        {
            Invoke-PureCloudWebRequest -Call:('/api/v2/routing/queues/{0}/users' -f $_.id) -WhatIf:$false | `
            Select-Object -ExpandProperty:'entities' | `
            ForEach-Object `
            {
                Get-DknPropertyValue -InputObject:$_ -Path:'user.email'
            }
        } | `
        Select-Object -Unique
    )
    WriteOutput ('{0} PureCloud agents' -f $agentEmailAddresses.Count)

    ############################################################################
    #
    # Grab all AD users that match the filter
    #
    ############################################################################
    WriteOutput ('Getting AD users...')
    $adUsers = @{}
    $adAttributes += @($attributeMappings | ForEach-Object { if ( $_.ContainsKey('AdAttribute') ) { $_['AdAttribute'] } })
    $adAttributes = @($adAttributes | Select-Object -Unique)
    $adUsersRaw = @(Get-ADObject -LDAPFilter:$ldapFilter -SearchBase:$adSearchBase -Property:$adAttributes)
    WriteOutput ('{0} AD users' -f $adUsersRaw.Count)
    if ( $isOneShot )
    {
        if ( $adUsersRaw.Count -ne 1 )
        {
            WriteOutput ('OneShot: Did not find exactly one user in AD. Found {0} object(s) matching "{1}"' -f $adUsersRaw.Count, $cnLdap)
            return
        }
        $oneShotAccountId = $adUsersRaw[0].extensionAttribute4
        if ( $oneShotAccountId -notmatch '^\d+$' )
        {
            WriteOutput ('OneShot: Account found in AD did not have extensionAttribute4 set to a valid value ("{0}")' -f $oneShotAccountId)
            return
        }
    }
    foreach ( $user in $adUsersRaw )
    {
        $key = $user.extensionAttribute4
        if ( $key -notmatch '^\d+$' ) { continue }

        # Agents should not be created as external contacts because they
        # already exist in the directory as agents
        if ( $user.mail -in $agentEmailAddresses ) { continue }

        $adUsers[$key] = $user
    }
    $adUsersRaw = @()

    ############################################################################
    #
    # Grab all PureCloud external contacts in the "DU" organisation
    #
    ############################################################################
    WriteOutput ('Getting PureCloud organizations...')
    $toDelete = New-Object -TypeName:'System.Collections.Generic.HashSet[string]'([StringComparer]::OrdinalIgnoreCase)
    $pcUsersByAccountId = @{}
    $pcUsersById = @{}
    $pcOrgs = @(Invoke-PureCloudWebRequest -Call:'/api/v2/externalcontacts/organizations' -WhatIf:$false | Select-Object -ExpandProperty:'entities' | Where-Object name -eq $pcOrganizationName)
    if ( $pcOrgs.Count -ne 1 -or $pcOrgs[0].name -ne $pcOrganizationName -or $null -eq $pcOrgs[0].id -or $pcOrgs[0].id.Length -ne 36 )
    {
        throw ('Unable to find PureCloud organization "{0}"' -f $pcOrganizationName)
    }
    $orgId = $pcOrgs[0].id

    WriteOutput ('Getting PureCloud contacts...')
    $limit = 200
    $cursor = ""
    $pcUsersRaw = @()
    do
    {
        if ([string]::IsNullOrWhiteSpace($cursor))
        {
            $singleResponse = @(Invoke-PureCloudWebRequest -Call:('/api/v2/externalcontacts/scan/contacts?limit={0}' -f $limit) -WhatIf:$false)
        }
        else
        {
            $singleResponse = @(Invoke-PureCloudWebRequest -Call:('/api/v2/externalcontacts/scan/contacts?limit={0}&cursor={1}' -f $limit, $cursor) -WhatIf:$false)
        }

        $cursors = $singleResponse | Select-Object -ExpandProperty: 'cursors'
        $entities = $singleResponse | Select-Object -ExpandProperty: 'entities'
        $pcUsersRaw += $entities

        if ($cursors | Get-Member -Name "after")
        {
            $cursor = $cursors.after
            WriteOutput ('{0} Cursor' -f $cursor)
        }
        else
        {
            $cursor = ""
        }
        WriteOutput ('{0} PureCloud users' -f $pcUsersRaw.Count)

    } while (-not [string]::IsNullOrWhiteSpace($cursor))
    # $pcUsersRaw = @(Invoke-PureCloudWebRequest -Call:('/api/v2/externalcontacts/organizations/{0}/contacts?pageSize={1}' -f $orgId, $pcPageSize) -WhatIf:$false | Select-Object -ExpandProperty:'entities')
    WriteOutput ('{0} PureCloud users' -f $pcUsersRaw.Count)
    foreach ( $user in $pcUsersRaw )
    {
        $userOrgId = Get-DknPropertyValue -InputObject:$user -Path:'externalOrganization.id'
        if ( $userOrgId -ne $orgId ) { continue }
        $pcUsersById[$user.id] = $user
        $key = Get-DknPropertyValue -InputObject:$user -Path:'address.address2'
        if ( $key -notmatch '^ID\d+$' )
        {
            $toDelete.Add($user.id) | Out-Null
            continue
        }
        $key = $key -replace '^ID'
        if ( $isOneShot -and $key -ne $oneShotAccountId ) { continue }
        $pcUsersByAccountId[$key] = $user
    }
    $pcUsersRaw = @()

    $extra      = @($pcUsersByAccountId.psbase.Keys | Where-Object { $_ -notin $adUsers.psbase.Keys            })
    $missing    = @($adUsers.psbase.Keys            | Where-Object { $_ -notin $pcUsersByAccountId.psbase.Keys })

    $stats = @{'Created' = 0; 'Deleted' = 0; 'Changed' = 0}
    ############################################################################
    #
    # Delete additional contacts from PureCloud
    #
    ############################################################################
    foreach ( $acctId in $extra )
    {
        $toDelete.Add($pcUsersByAccountId[$acctId].id) | Out-Null
    }
    WriteOutput (
        'AdCount="{0}",PureCloudCount="{1}",Create="{2}",Delete="{3}"' -f
        $adUsers.psbase.Keys.Count,         # 0
        $pcUsersById.psbase.Keys.Count,     # 1
        $missing.Count,                     # 2
        $toDelete.Count                     # 3
    )

    if ( $toDelete.Count -gt $maxDeletionsPerRun )
    {
        throw (
            'Too many deletions queued to occur this run ({0}, threshold {1})' -f
            $toDelete.Count,
            $maxDeletionsPerRun
        )
    }

    foreach ( $pcId in $toDelete )
    {
        WriteOutput (
            'Action="Delete",Id="{0}",Name="{1} {2}"' -f
            $pcId,                              # 0
            $pcUsersById[$pcId].firstName,      # 1
            $pcUsersById[$pcId].lastName        # 2
        )
        $stats['Deleted']++
        Invoke-PureCloudWebRequest -Call:('/api/v2/externalcontacts/contacts/{0}' -f $pcId) -Method:'DELETE'
    }

    ############################################################################
    #
    # Create contacts that are missing from PureCloud
    #
    ############################################################################
    :nextAccount foreach ( $acctId in $missing )
    {
        $adUser = $adUsers[$acctId]
        $name = ('{0} {1}' -f $adUser.givenName, $adUser.sn).Trim()
        WriteOutput ('Action="Create",AccountId="{0}",Name="{1}"' -f $acctId, $name)
        $body = @{
            'firstName'             = $adUser.givenName
            'lastName'              = $adUser.sn
            'externalOrganization'  = @{ 'id' = $orgId }
            'address'               = @{ 'address2' = 'ID' + $acctId }
        }
        $body = Get-LdrRequestBody -Body:$body
        $url = '/api/v2/externalcontacts/contacts'
        $res = $null
        try
        {
            $res = Invoke-PureCloudWebRequest -Call:$url -Method:'POST' -Body:$body
        }
        catch [Net.WebException]
        {
            if ( $_.Exception.Status -eq [Net.WebExceptionStatus]::ProtocolError )
            {
                $response = $_.Exception.Response
                if ( $null -ne $response -and $response.StatusCode.value__ -in @(400, 422) )
                {
                    WriteOutput ('Error: ' + $_.Exception.Message) -Type:'Warning'
                    WriteOutput ('WebException POST {0}' -f $url) -Type:'Warning'
                    WriteOutput ('Body: ' + ($body | ConvertTo-Json)) -Type:'Warning'
                    if ( Test-Member -InputObject:$_ -Property:'ErrorDetails' )
                    {
                        WriteOutput $_.ErrorDetails -Type:'Warning'
                    }
                    QUEUE_SOC_MESSAGE -Level:'Warning' -Message:('Unable to create "{0}" ({1}): {2}' -f $name, $acctId, $_.Exception.Message)
                    continue nextAccount
                }
            }
            throw
        }
        if ( $WhatIfPreference -and $null -eq $res )
        {
            $res = $body
            $res['id'] = ('whatif..-to..-be..-crea-ted..{0}' -f ($acctId -replace '^(.{1,7}).*', '$1').PadLeft(7, '.'))
            $res['createDate'] = [DateTime]::Now.ToString('u')
            $res['modifyDate'] = [DateTime]::Now.ToString('u')
        }
        else
        {
            Start-Sleep -Seconds:1  # Try not to smash them too much.
        }
        $pcId = Get-DknPropertyValue -InputObject:$res -Path:'id'
        if ( $null -eq $res )
        {
            QUEUE_SOC_MESSAGE -Level:'Warning' -Message:('Failed to create user {0} ({1}), nothing returned from creation API call' -f $name, $acctId)
            continue
        }
        if ( $null -eq $pcId -or $pcId.Length -ne 36 )
        {
            QUEUE_SOC_MESSAGE -Level:'Warning' -Message:('Failed to create user {0} ({1}), invalid PureCloud ID returned "{2}"' -f $name, $acctId, $pcId)
            continue
        }
        $stats['Created']++
        $pcUsersByAccountId[$acctId] = $res
    }

    ############################################################################
    #
    # Check for differences
    #
    ############################################################################
    $rapidChangesThisRun = 0
    $nowTime = [DateTime]::Now
    :nextAccount foreach ( $acctId in $adUsers.psbase.Keys )
    {
        $adUser = $adUsers[$acctId]
        $pcUser = $pcUsersByAccountId[$acctId]
        $pcId = (Get-DknPropertyValue -InputObject:$pcUser -Path:'id')
        if ( [string]::IsNullOrEmpty($pcId) )
        {
            QUEUE_SOC_MESSAGE -Level:'Warning' -Message:(
                'Did not find PureCloud user for ACCOUNT_ID "{0}" but was expecting to' -f
                $acctId
            )
            continue nextAccount
        }
        $recentlyModified = $false
        $createDate = Get-DknPropertyValue -InputObject:$pcUser -Path:'createDate'
        $modifyDate = Get-DknPropertyValue -InputObject:$pcUser -Path:'modifyDate'
        if ( $createDate -match '^2[0-9\-: T]+Z$' ) { $createDate = [DateTime]$createDate }
        if ( $modifyDate -match '^2[0-9\-: T]+Z$' ) { $modifyDate = [DateTime]$modifyDate }
        if ( $modifyDate -is [DateTime] -and $createDate -is [DateTime] )
        {
            if ( $nowTime.Subtract($createDate) -gt [TimeSpan]::FromHours(48) `
                -and $nowTime.Subtract($modifyDate) -le [TimeSpan]::FromHours(48) )
            {
                # Created more than 48 hours ago, and modified in the last 48
                # hours, we've touched this object recently.
                $recentlyModified = $true
            }
        }
        else
        {
            WriteOutput ('Action="Warning",AccountId="{0}",Id="{1}",Message="Invalid creation or modification date",Created="{2}",Modified="{3}"' -f $acctId, $pcId, $createDate, $modifyDate)
            $recentlyModified = $true
        }

        $expected = @{}
        $hasDiff = $false
        $phoneWork = Format-PureCloudPhoneNumber -Number:$adUser.telephoneNumber
        $phoneMob = Format-PureCloudPhoneNumber -Number:$adUser.mobile
        foreach ( $mapping in $attributeMappings )
        {
            $adValue = $null
            $pcValue = $null
            if ( $mapping.ContainsKey('AdAttribute') )  { $adValue = $adUser.($mapping['AdAttribute']) }
            if ( $mapping.ContainsKey('AdValue') )      { $adValue = & $mapping['AdValue'] }
            if ( $mapping.ContainsKey('PcAttribute') )  { $pcValue = Get-DknPropertyValue -InputObject:$pcUser -Path:$mapping['PcAttribute'] }
            if ( $mapping.ContainsKey('PcValue') )      { $pcValue = & $mapping['PcValue'] }

            if ( [string]::IsNullOrEmpty($adValue) -eq $false )
            {
                # SORRY: I'm so sorry I wasn't smart enough to do this in a
                # more simple easy to understand way. I hope you don't have to
                # edit/understand the next bit of code, hopefully I was at
                # least smart enough to not make a mistake. Essentially what
                # I'm trying to do is build deep objects from a simple property
                # path, i.e.
                # 'workPhone.e164' = 'value'
                # ->
                # @{'workPhone' = @{'e164' = 'value'}}
                $parts = @($mapping['PcAttribute'] -split '\.')
                $indexHigh = $parts.Count - 1
                $depthPosition = @($expected)
                for ( $i = 0; $i -le $indexHigh; $i++ )
                {
                    $propName = $parts[$i]
                    if ( $depthPosition[$i].ContainsKey($propName) -eq $false )
                    {
                        if ( $i -eq $indexHigh )
                        {
                            $depthPosition[$i][$propName] = $adValue
                        }
                        else
                        {
                            $depthPosition[$i][$propName] = @{}
                        }
                    }
                    $depthPosition += $depthPosition[$i][$propName]
                }
                # End terribleness (well, there are degrees of terribleness I guess)
            }

            $attributeHasDiff = if ( $mapping.ContainsKey('Diff') ) { & $mapping['Diff'] } else { $adValue -cne $pcValue }
            if ( $attributeHasDiff -isnot [bool] -or $attributeHasDiff )
            {
                $hasDiff = $true
                WriteOutput (
                    'Action="Diff",AccountId="{0}",Username="{1}",Id="{2}",Attribute="{3}",Expected="{4}",Actual="{5}"' -f
                    $acctId,                    # 0
                    $adUser.cn,                 # 1
                    $pcId,                      # 2
                    $mapping['PcAttribute'],    # 3
                    $adValue,                   # 4
                    $pcValue                    # 5
                )
            }
        }
        if ( $hasDiff )
        {
            if ( $recentlyModified ) { $rapidChangesThisRun++ }
            if ( $rapidChangesThisRun -gt $maxRapidChanges )
            {
                throw (
                    'Too many changes to recently modified accounts this run ({0}, threshold {1})' -f
                    $rapidChangesThisRun,
                    $maxRapidChanges
                )
            }
            $stats['Changed']++
            $body = Get-LdrRequestBody -Body:$expected
            $url = '/api/v2/externalcontacts/contacts/{0}' -f $pcId
            $res = $null
            try
            {
                $res = Invoke-PureCloudWebRequest -Call:$url -Method:'PUT' -Body:$body
            }
            catch [Net.WebException]
            {
                if ( $_.Exception.Status -eq [Net.WebExceptionStatus]::ProtocolError )
                {
                    $response = $_.Exception.Response
                    if ( $null -ne $response -and $response.StatusCode.value__ -in @(400, 422) )
                    {
                        WriteOutput ('Error: ' + $_.Exception.Message) -Type:'Warning'
                        WriteOutput ('WebException PUT {0}' -f $url) -Type:'Warning'
                        WriteOutput ('Body: ' + ($body | ConvertTo-Json)) -Type:'Warning'
                        if ( Test-Member -InputObject:$_ -Property:'ErrorDetails' )
                        {
                            WriteOutput $_.ErrorDetails -Type:'Warning'
                        }
                        QUEUE_SOC_MESSAGE -Level:'Warning' -Message:(
                            'Unable to update ACCOUNT_ID {0}: {1}' -f
                            $acctId,
                            $_.Exception.Message
                        )
                        continue nextAccount
                    }
                }
                throw
            }
            Start-Sleep -Seconds:1  # Try not to smash them too much.
        }
    }
    QUEUE_SOC_MESSAGE -Level:'Ok' -Message:(
        'Created="{0}",Deleted="{1}",Changed="{2}",RapidChanges="{3}"' -f
        $stats['Created'],
        $stats['Deleted'],
        $stats['Changed'],
        $rapidChangesThisRun
    )
}
catch
{
    Write-DknException -Exception:$_
}
finally
{
    REPORT_SOC_MESSAGES
    Stop-DknScriptLogging -Confirm:$false
}

# cSpell:ignore actioned organisation psbase whatif crea isnot