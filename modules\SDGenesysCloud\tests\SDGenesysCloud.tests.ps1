[CmdletBinding(SupportsShouldProcess = $false)]
[System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("PSUseCompatibleCommands", "")]
Param()

#Requires -Version 7.2
#Requires -Modules @{ ModuleName = 'Pester'; ModuleVersion = '5.0' }

#region Discovery

$ModuleName = 'SDGenesysCloud'

#endregion Discovery

BeforeAll {
    $ModuleName = 'SDGenesysCloud'
    Import-Module -Name:"${PSScriptRoot}\..\${ModuleName}.psm1" -Force -Verbose:$false
}

Describe "$ModuleName Sanity Tests - Help Content" -Tags 'Module' {

    #region Discovery

    # The module will need to be imported during Discovery since we're using it to generate test cases / Context blocks
    Import-Module -Name:"${PSScriptRoot}\..\${ModuleName}.psm1" -Force -Verbose:$false

    $shouldProcessParameters = 'WhatIf', 'Confirm'

    # Generate command list for generating Context / TestCases
    $Module = Get-Module $ModuleName
    $CommandList = @(
        $Module.ExportedFunctions.Keys
        $Module.ExportedCmdlets.Keys
    )

    #endregion Discovery

    foreach ($Command in $CommandList)
    {
        Context "$Command - Help Content" {

            #region Discovery
            $Help = @{ Help = Get-Help -Name:$Command -Full | Select-Object -Property:'*' }
            $Parameters = Get-Help -Name:$Command -Parameter:'*' -ErrorAction:'Ignore' |
                Where-Object { $_.name -and $_.name -NotIn $shouldProcessParameters } |
                ForEach-Object {
                    @{
                        Name        = $_.name
                        Description = $_.description.Text
                    }
                }
            $Ast = @{
                # Ast will be $null if the command is a compiled cmdlet
                Ast        = (Get-Content -Path:"function:/$Command" -ErrorAction:'Ignore').Ast
                Parameters = $Parameters
            }
            $Examples = $Help.Help.Examples.Example | ForEach-Object { @{ Example = $_ } }

            #endregion Discovery

            It "has help content for $Command" -TestCases $Help {
                $Help | Should -Not -BeNullOrEmpty
            }

            It "contains a synopsis for $Command" -TestCases $Help {
                $Help.Synopsis | Should -Not -BeNullOrEmpty
            }

            It "contains a description for $Command" -TestCases $Help {
                $Help.Description | Should -Not -BeNullOrEmpty
            }

#            It "lists the function author in the Notes section for $Command" -TestCases $Help {
#                $Notes = $Help.AlertSet.Alert.Text -split '\n'
#                $Notes[0].Trim() | Should -BeLike "Author: *"
#            }

            # This will be skipped for compiled commands ($Ast.Ast will be $null)
            $params =
            @{
                TestCases = $Ast
                Skip = -not ($Parameters -and $Ast.Ast)
            }
            It "has a help entry for all parameters of $Command" @params {
                $because = 'the number of parameters in the help should match the number in the function script'
                @($Parameters).Count | Should -Be $Ast.Body.ParamBlock.Parameters.Count -Because $because
            }

            It "has a description for $Command parameter -<Name>" -TestCases $Parameters -Skip:(-not $Parameters) {
                $Description | Should -Not -BeNullOrEmpty -Because "parameter $Name should have a description"
            }

            It "has at least one usage example for $Command" -TestCases $Help {
                $Help.Examples.Example.Code.Count | Should -BeGreaterOrEqual 1
            }

            It "lists a description for $Command example: <Title>" -TestCases $Examples {
                $because = "example $($Example.Title) should have a description!"
                $Example.Remarks | Should -Not -BeNullOrEmpty -Because $because
            }
        }
    }
}
