#Requires -Version 7
$ErrorActionPreference = 'Stop'
$ProgressPreference = 'SilentlyContinue'

'Setting up dependencies...' | Out-Default

$requiredModules =
@(
    'Pester'
)
$installedModules = @(Get-Module -ListAvailable).Name
foreach ( $module in $requiredModules )
{
    if ( $module -NotIn $installedModules )
    {
        Install-Module -Force -Name:$module
    }
}

'Running Pester...' | Out-Default
#$config = [PesterConfiguration]::Default
#$config.TestResult.OutputPath = './build/unit-test-job.xml'
#$config.TestResult.OutputFormat = 'JUnitXml'
#$config.Filter.ExcludeTag = 'unstable'
#$config.Run.PassThru = $true
$params =
@{
    OutputFile = './build/unit-test-job.xml'
    OutputFormat = 'JUnitXml'
    PassThru = $true
    ExcludeTag = 'unstable'
}
$results = Invoke-Pester @params
if ( $results.FailedCount -gt 0 )
{
    exit 1
}
