{
    "cSpell.allowCompoundWords": true,
    "cSpell.diagnosticLevel": "Information",
    "cSpell.enableFiletypes": [
        "powershell"
    ],
    "cSpell.ignoreWords": [
        "PassThru"
    ],
    "cSpell.words": [
        "<PERSON><PERSON><PERSON>",
        "deakin",
        "<PERSON><PERSON>s",
        "mypurecloud",
        "SDGC"
    ],
    // Use a custom PowerShell Script Analyzer settings file for this workspace.
    // Relative paths for this setting are always relative to the workspace root dir.
    "powershell.scriptAnalysis.settingsPath": "./PSScriptAnalyzerSettings.psd1"
}