stages:
  - test

default:
#  image:
#    # https://hub.docker.com/_/microsoft-powershell
#    #name: "mcr.microsoft.com/powershell:latest"
#    # https://hub.docker.com/_/microsoft-powershell-test-deps
#    name: "mcr.microsoft.com/powershell/test-deps:ubuntu-18.04"
  before_script:
  #region install pwsh
  # https://docs.microsoft.com/en-us/powershell/scripting/install/install-alpine
  - apk add --no-cache
      ca-certificates
      less
      ncurses-terminfo-base
      krb5-libs
      libgcc
      libintl
      libssl1.1
      libstdc++
      tzdata
      userspace-rcu
      zlib
      icu-libs
      curl
  - apk -X https://dl-cdn.alpinelinux.org/alpine/edge/main add --no-cache
      lttng-ust
  - curl -L
      https://github.com/PowerShell/PowerShell/releases/download/v7.2.0/powershell-7.2.0-linux-alpine-x64.tar.gz
      -o /tmp/powershell.tar.gz
  - mkdir -p /opt/microsoft/powershell/7
  - tar zxf /tmp/powershell.tar.gz -C /opt/microsoft/powershell/7
  - chmod +x /opt/microsoft/powershell/7/pwsh
  - ln -s /opt/microsoft/powershell/7/pwsh /usr/bin/pwsh
  #endregion install pwsh
lint-test-job:
  stage: test
  script:
    - echo "Running lint tests..."
    - pwsh -noninteractive -file build/lint-test-job.ps1

unit-test-job:
  stage: test
  script:
    - echo "Running unit tests..."
    - pwsh -noninteractive -file build/unit-test-job.ps1
  artifacts:
    paths:
      - build/unit-test-job.xml
    reports:
      junit: build/unit-test-job.xml

# cSpell:ignore pwsh deps libstdc tzdata lttng mkdir