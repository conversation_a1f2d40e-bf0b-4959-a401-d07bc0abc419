<#
.SYNOPSIS

Create an alias on each Genesys Cloud user

.DESCRIPTION

In order to avoid staff/students contacting Contact Centre staff directly,
there is a requirement to update the agent alias field in GC.

This script updates user profiles to set the agent name (alias) to the user's first name only.
The agent name is used for canned responses and digital interactions like web messaging and web chat.

This is scripted to ensure if agents change this, we can set this back to a controlled name.

EFFICIENT UPDATE LOGIC:
- Retrieves current alias from each user's profile via /api/v2/users/{userId}/profile
- Compares current alias with desired alias (override or first name)
- Only makes update API calls when current alias differs from desired alias
- Skips unnecessary updates when alias is already correct, improving performance

.PARAMETER LogDirectory
Directory path for logging script execution

.PARAMETER PageSize
Number of users to retrieve per API call (default: 500)

.PARAMETER SpecificUsers
Array of specific usernames/emails to process (if provided, only these users will be updated)

.PARAMETER AliasOverrideFile
Path to a JSON file containing custom agent alias mappings.
Format: {"userId1": "CustomAlias1", "userId2": "CustomAlias2"}
Where keys are Genesys Cloud user IDs (GUIDs) and values are desired alias names.

.PARAMETER WhatIf
Show what changes would be made without actually making them

.EXAMPLE
.\GENESYS-AGENT-ALIASES.ps1 -WhatIf

.EXAMPLE
.\GENESYS-AGENT-ALIASES.ps1 -Verbose

.EXAMPLE
.\GENESYS-AGENT-ALIASES.ps1 -SpecificUsers @("<EMAIL>", "<EMAIL>") -Verbose

.EXAMPLE
.\GENESYS-AGENT-ALIASES.ps1 -AliasOverrideFile "C:\config\agent-aliases.json" -WhatIf

.EXAMPLE
.\GENESYS-AGENT-ALIASES.ps1 -SpecificUsers @("<EMAIL>") -AliasOverrideFile ".\custom-aliases.json" -Verbose

.NOTES
This script aligns with the Genesys Cloud API best practices used in PURECLOUD-PROVISIONING-AGENTS.ps1
Uses the standard /api/v2/users/{userId} PATCH endpoint instead of the profile-specific endpoint.

#>
[CmdletBinding(SupportsShouldProcess = $true)]
Param
(
    [Parameter()]
    [string]$CredentialName = 'Genesys Prod User OAuth',

    [Parameter()]
    [string]$LogDirectory = 'C:\logs\local\GENESYS-AGENT-ALIASES',

    [Parameter()]
    [int]$PageSize = 500,

    [Parameter()]
    [string[]]$SpecificUsers = @(),

    [Parameter()]
    [string]$AliasOverrideFile = $null,

    [Parameter()]
    [switch]$Force
)

$ErrorActionPreference = 'Stop'

Write-Output "Starting GENESYS-AGENT-ALIASES.ps1... ${PSScriptRoot}"

Import-Module -Name:"${PSScriptRoot}\..\modules\SDSecretManagement" -Verbose:$false
Import-Module -Name:"${PSScriptRoot}\..\modules\SDGenesysCloud" -Verbose:$false

# Initialize logging
$logPath = $null
if ( [string]::IsNullOrEmpty($LogDirectory) -eq $false )
{
    $scriptName = ([IO.FileInfo]$script:MyInvocation.MyCommand.Path).BaseName
    $logPath = Join-Path -Path:$LogDirectory -ChildPath:(
        $scriptName + '.' + [DateTime]::Now.ToString('yyyy-MM-dd-HH-mm') + '.log'
    )
    Start-Transcript -Path:$logPath -Append
}

# Get credentials for Genesys Cloud API
$apiBaseCred = (Get-SDCredential $CredentialName)
if ($null -eq $apiBaseCred) {
    throw "Failed to retrieve credentials for '$CredentialName'"
}

# Load alias override configuration if specified
$aliasOverrides = @{}
if (-not [string]::IsNullOrEmpty($AliasOverrideFile)) {
    Write-Output "Loading alias override configuration from: $AliasOverrideFile"

    try {
        # Check if file exists
        if (-not (Test-Path -Path $AliasOverrideFile -PathType Leaf)) {
            throw "Alias override file not found: $AliasOverrideFile"
        }

        # Read and parse JSON file
        $jsonContent = Get-Content -Path $AliasOverrideFile -Raw -ErrorAction Stop
        if ([string]::IsNullOrWhiteSpace($jsonContent)) {
            throw "Alias override file is empty: $AliasOverrideFile"
        }

        $aliasOverrides = $jsonContent | ConvertFrom-Json -AsHashtable -ErrorAction Stop

        # Validate the structure and content
        $validOverrides = @{}
        $invalidCount = 0

        foreach ($userId in $aliasOverrides.Keys) {
            $aliasName = $aliasOverrides[$userId]

            # Validate user ID format (GUID)
            if ($userId -notmatch '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$') {
                Write-Warning "Invalid user ID format in override file: '$userId' (must be a GUID)"
                $invalidCount++
                continue
            }

            # Validate alias name (not empty or whitespace)
            if ([string]::IsNullOrWhiteSpace($aliasName)) {
                Write-Warning "Empty or whitespace alias name for user ID: '$userId'"
                $invalidCount++
                continue
            }

            # Trim whitespace and add to valid overrides
            $validOverrides[$userId] = $aliasName.Trim()
        }

        $aliasOverrides = $validOverrides

        Write-Output "Loaded $($aliasOverrides.Count) valid alias overrides"
        if ($invalidCount -gt 0) {
            Write-Warning "Skipped $invalidCount invalid entries from override file"
        }

        # Log the overrides in verbose mode
        if ($VerbosePreference -eq 'Continue') {
            Write-Verbose "Alias overrides loaded:"
            foreach ($userId in $aliasOverrides.Keys) {
                Write-Verbose "  $userId -> '$($aliasOverrides[$userId])'"
            }
        }

    } catch {
        throw "Failed to load alias override file '$AliasOverrideFile': $($_.Exception.Message)"
    }
} else {
    Write-Verbose "No alias override file specified - using default first name logic"
}

# Get all users from Genesys Cloud (simple approach)
Write-Output "Retrieving user list from Genesys Cloud..."

# Get all users
$response = $null
try {
    $response = Invoke-PureCloudWebRequest -Call:"/api/v2/users?pageSize=$PageSize" -WhatIf:$false
}
catch [Net.WebException]
{
    if ( $_.Exception.Status -eq [Net.WebExceptionStatus]::ProtocolError )
    {
        $httpResponse = $_.Exception.Response
        if ( $null -ne $httpResponse -and $httpResponse.StatusCode.value__ -in @(400, 422) )
        {
            Write-Warning ('Error: ' + $_.Exception.Message)
            Write-Warning ('WebException GET /api/v2/users?pageSize={0}' -f $PageSize)
            if ( $_.PSObject.Properties['ErrorDetails'] -and $_.ErrorDetails )
            {
                Write-Warning $_.ErrorDetails
            }
            throw ('Unable to retrieve users from Genesys Cloud: {0}' -f $_.Exception.Message)
        }
    }
    throw
}
catch {
    throw "Failed to retrieve users from Genesys Cloud: $($_.Exception.Message)"
}

if ($null -eq $response) {
    throw "No response received from Genesys Cloud API"
}

$allUsers = $response.entities

# Filter users based on SpecificUsers parameter using PowerShell
if ($SpecificUsers.Count -gt 0) {
    Write-Output "Filtering to specific users: $($SpecificUsers -join ', ')"
    $users = @($allUsers | Where-Object { $_.username -in $SpecificUsers -or $_.email -in $SpecificUsers })

    # Report which users were found/not found
    foreach ($requestedUser in $SpecificUsers) {
        $found = $users | Where-Object { $_.username -eq $requestedUser -or $_.email -eq $requestedUser }
        if ($found) {
            Write-Verbose "Found user: $requestedUser -> $($found.name)"
        } else {
            Write-Warning "User not found: $requestedUser"
        }
    }
} else {
    # Use all users
    $users = $allUsers
}

if ($null -eq $users -or $users.Count -eq 0) {
    throw "No users retrieved from Genesys Cloud API"
}

Write-Output "Retrieved $($users.Count) users from Genesys Cloud"

# Create lookup tables for efficient processing (following provisioning script pattern)
$usersById = @{}
$usersByEmail = @{}
foreach ($user in $users) {
    $usersById[$user.id] = $user
    if (-not [string]::IsNullOrEmpty($user.username)) {
        $usersByEmail[$user.username] = $user
    }
}

# Process users for alias updates with efficient update logic
Write-Output "Processing $($users.Count) users for alias updates..."
Write-Output "Efficient update mode: Only updating when current alias differs from desired alias"
$processedCount = 0
$updatedCount = 0
$skippedCount = 0
$noUpdateNeededCount = 0
$apiCallCount = 0

# Process each user (following the pattern from PURECLOUD-PROVISIONING-AGENTS.ps1)
foreach ($user in $users) {
    $processedCount++
    Write-Progress -Activity "Processing User Aliases" -Status "Processing user $processedCount of $($users.Count)" -PercentComplete (($processedCount / $users.Count) * 100)

    try {
        $userId = $user.id
        $username = $user.username
        $currentName = $user.name

        # Skip users without proper name or email
        if ([string]::IsNullOrEmpty($currentName) -or [string]::IsNullOrEmpty($username)) {
            Write-Warning "Skipping user $userId - missing name or username"
            $skippedCount++
            continue
        }

        # Calculate desired agent alias - check overrides first, then fall back to first name
        $desiredAlias = $null
        $aliasSource = "default"

        # Check if user has an override alias configured
        if ($aliasOverrides.ContainsKey($userId)) {
            $desiredAlias = $aliasOverrides[$userId]
            $aliasSource = "override"
            Write-Verbose "Using override alias for $username ($userId): '$desiredAlias'"
        } else {
            # Fall back to default logic (first name only)
            $nameParts = $currentName -split '\s+'
            $desiredAlias = $nameParts[0]
            $aliasSource = "first name"
            Write-Verbose "Using first name as alias for ${username}: '${desiredAlias}'"
        }

        if ([string]::IsNullOrEmpty($desiredAlias)) {
            Write-Warning "Skipping user $username - cannot determine alias (source: $aliasSource, name: '$currentName')"
            $skippedCount++
            continue
        }

        # Get current user profile to check agent alias (need to call profile API)
        Write-Verbose "Getting profile for user: $username"
        $userProfile = $null
        try {
            $userProfile = Invoke-PureCloudWebRequest -Call:"/api/v2/users/$userId/profile" -WhatIf:$false
            $apiCallCount++  # Count profile retrieval API calls
        }
        catch [Net.WebException]
        {
            if ( $_.Exception.Status -eq [Net.WebExceptionStatus]::ProtocolError )
            {
                $response = $_.Exception.Response
                if ( $null -ne $response -and $response.StatusCode.value__ -in @(400, 422) )
                {
                    Write-Warning ('Error: ' + $_.Exception.Message)
                    Write-Warning ('WebException GET /api/v2/users/{0}/profile' -f $userId)
                    if ( $_.PSObject.Properties['ErrorDetails'] -and $_.ErrorDetails )
                    {
                        Write-Warning $_.ErrorDetails
                    }
                    Write-Warning ('Unable to retrieve profile for "{0}" ({1}): {2}' -f $currentName, $userId, $_.Exception.Message)
                    $skippedCount++
                    continue
                }
            }
            throw
        }
        catch {
            Write-Warning "Failed to retrieve profile for ${currentName}: $($_.Exception.Message)"
            $skippedCount++
            continue
        }

        if ($null -eq $userProfile) {
            Write-Warning "Skipping user $username - unable to retrieve profile"
            $skippedCount++
            continue
        }

        # Check current agent alias from profile
        $currentAlias = $null
        $agentExists = $false
        $existingAgentNameId = $null

        if ($userProfile.PSObject.Properties['agent'] -and $userProfile.agent) {
            $agentExists = $true
            if ($userProfile.agent.PSObject.Properties['name'] -and $userProfile.agent.name) {
                if ($userProfile.agent.name -is [array] -and $userProfile.agent.name.Count -gt 0) {
                    $currentAlias = $userProfile.agent.name[0].value
                    $existingAgentNameId = $userProfile.agent.name[0]._id
                } elseif ($userProfile.agent.name.PSObject.Properties['value']) {
                    $currentAlias = $userProfile.agent.name.value
                    $existingAgentNameId = $userProfile.agent.name._id
                }
            }
        }

        # Debug output only for verbose mode
        Write-Verbose "Profile structure for ${username}: Agent exists: $agentExists, Current alias: '$currentAlias'"

        # Efficient Update Logic: Compare current vs desired alias
        $updateRequired = $false
        $updateReason = ""

        if ([string]::IsNullOrEmpty($currentAlias)) {
            $updateRequired = $true
            $updateReason = "No alias currently set"
            Write-Verbose "✓ UPDATE NEEDED - User $username has no agent alias set, will set to: '$desiredAlias'"
        }
        elseif ($currentAlias -cne $desiredAlias) {
            $updateRequired = $true
            $updateReason = "Alias mismatch"
            Write-Verbose "✓ UPDATE NEEDED - User $username alias differs: '$currentAlias' -> '$desiredAlias'"
        }
        else {
            $updateRequired = $false
            $updateReason = "Alias already correct"
            Write-Verbose "⚡ SKIP UPDATE - User $username alias already correct: '$currentAlias' (matches desired: '$desiredAlias')"
            $noUpdateNeededCount++
        }

        if ($updateRequired) {
            $sourceInfo = if ($aliasSource -eq "override") { " [OVERRIDE]" } else { "" }
            Write-Output "🔄 UPDATING: $currentName ($username) - Reason: $updateReason - '$currentAlias' -> '$desiredAlias'$sourceInfo"

            if ($PSCmdlet.ShouldProcess("User $username", "Update agent alias to '$desiredAlias'")) {
                # Build the profile update body - use the original working approach
                $profileBody = @{
                    version = $userProfile.version
                }

                # Create or update the agent section with correct structure
                if ($agentExists -and $existingAgentNameId) {
                    # Update existing agent alias - preserve existing structure but update value
                    $profileBody.agent = @{
                        name = @{
                            labelKey = "name"
                            value = $desiredAlias
                            _id = $existingAgentNameId
                        }
                    }
                    Write-Verbose "Updating existing agent alias with ID: $existingAgentNameId"
                } else {
                    # Create new agent alias - use simple structure without _id
                    $profileBody.agent = @{
                        name = @{
                            labelKey = "name"
                            value = $desiredAlias
                        }
                    }
                    Write-Verbose "Creating new agent alias"
                }

                Write-Verbose "Profile update body: $($profileBody | ConvertTo-Json -Depth 10)"

                try {
                    # Use the profile API endpoint (this is the correct endpoint for agent alias)
                    $response = Invoke-PureCloudWebRequest -Call:"/api/v2/users/$userId/profile" -Method:'PUT' -Body:($profileBody | ConvertTo-Json -Depth 10)
                    $apiCallCount++  # Count update API calls
                    Write-Verbose "✅ Successfully updated alias for $currentName to '$desiredAlias'"
                    $updatedCount++
                }
                catch [Net.WebException]
                {
                    if ( $_.Exception.Status -eq [Net.WebExceptionStatus]::ProtocolError )
                    {
                        $response = $_.Exception.Response
                        if ( $null -ne $response -and $response.StatusCode.value__ -in @(400, 422) )
                        {
                            Write-Warning ('Error: ' + $_.Exception.Message)
                            Write-Warning ('WebException PUT /api/v2/users/{0}/profile' -f $userId)
                            Write-Warning ('Body: ' + ($profileBody | ConvertTo-Json -Depth 10))
                            if ( $_.PSObject.Properties['ErrorDetails'] -and $_.ErrorDetails )
                            {
                                Write-Warning $_.ErrorDetails
                            }
                            Write-Warning ('Unable to update agent alias for "{0}" ({1}): {2}' -f $currentName, $userId, $_.Exception.Message)
                            $skippedCount++
                            continue
                        }
                    }
                    throw
                }
                catch {
                    Write-Warning "❌ Failed to update alias for ${currentName}: $($_.Exception.Message)"
                    $skippedCount++
                    continue
                }
            }
        } else {
            # No update needed - alias already correct
            Write-Verbose "⚡ SKIPPED: $currentName ($username) - Alias already correct: '$currentAlias'"
        }
    }
    catch {
        Write-Warning "Failed to process user ${userId}: $($_.Exception.Message)"
        continue
    }
}

Write-Progress -Activity "Processing User Aliases" -Completed

# Calculate override usage statistics
$overrideUsedCount = 0
if ($aliasOverrides.Count -gt 0) {
    foreach ($user in $users) {
        if ($aliasOverrides.ContainsKey($user.id)) {
            $overrideUsedCount++
        }
    }
}

Write-Output ""
Write-Output "=== PROCESSING SUMMARY ==="
Write-Output "  Total users processed: $processedCount"
Write-Output "  Users updated: $updatedCount"
Write-Output "  Users skipped (errors): $skippedCount"
Write-Output "  Users with correct alias (no update needed): $noUpdateNeededCount"
Write-Output "  Success rate: $(if ($processedCount -gt 0) { [math]::Round(($updatedCount / $processedCount) * 100, 2) } else { 0 })%"
Write-Output ""
Write-Output "=== PERFORMANCE METRICS ==="
Write-Output "  Total API calls made: $apiCallCount"
Write-Output "  Profile retrieval calls: $processedCount"
Write-Output "  Update calls: $updatedCount"
Write-Output "  API calls saved by efficiency logic: $noUpdateNeededCount"
Write-Output "  Efficiency ratio: $(if ($processedCount -gt 0) { [math]::Round((($processedCount - $updatedCount) / $processedCount) * 100, 2) } else { 0 })% of users required no updates"

if ($aliasOverrides.Count -gt 0) {
    Write-Output ""
    Write-Output "=== ALIAS OVERRIDE SUMMARY ==="
    Write-Output "  Override file loaded: $AliasOverrideFile"
    Write-Output "  Total overrides defined: $($aliasOverrides.Count)"
    Write-Output "  Overrides applied: $overrideUsedCount"
    Write-Output "  Unused overrides: $($aliasOverrides.Count - $overrideUsedCount)"

    if ($VerbosePreference -eq 'Continue' -and ($aliasOverrides.Count - $overrideUsedCount) -gt 0) {
        Write-Verbose "Unused override user IDs:"
        foreach ($overrideUserId in $aliasOverrides.Keys) {
            $userFound = $users | Where-Object { $_.id -eq $overrideUserId }
            if (-not $userFound) {
                Write-Verbose "  $overrideUserId -> '$($aliasOverrides[$overrideUserId])' (user not found in current batch)"
            }
        }
    }
}

}
catch
{
    Write-Error $_.Exception.Message
    throw
}
finally
{
    if ($logPath) {
        Write-Output "Log file: $logPath"
        Stop-Transcript
    }
}
#cSpell:ignore cPrimary, Cconversation, Cout, Cgeolocation, Cpresence, Clocations, Cauthorization, Johansson