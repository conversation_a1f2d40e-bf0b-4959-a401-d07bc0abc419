<#
.SYNOPSIS

Interface with the SOCQ API.

#>

#region Exported functions
#
#

function Add-SDSOCQServiceResult
{
    <#
    .SYNOPSIS

    Add a service result to the queue to be submitted to the server.

    .DESCRIPTION

    Add a service result to a queue to be submitted to the server later.

    Allows multiple "composite" entries to be submitted as the result for a
    single SOC service.

    The results are combined as a single submission to SOC, and ordered by
    status and priority.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([void])]
    Param
    (
        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [string]
        # The name of the host as defined in the SOC service definition.
        # If not specified will use the DefaultHostName provided on
        # initialization.
        $HostName = $script:SocqSession.DefaultHostName,

        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [Alias('ServiceDefinition')]
        [string]
        # The name of the service definition.
        # If not specified will use the DefaultServiceName provided on
        # initialization.
        $ServiceName = $script:SocqSession.DefaultService,

        [Parameter(Mandatory = $true)]
        [ValidateSet('Ok', 'Warning', 'Critical', 'Unknown')]
        [string]
        # The status of the result to submit.
        $Status,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]
        # The text output to submit to SOC
        $Output,

        [Parameter()]
        [ValidateRange(1, 100)]
        [int]
        # The priority to put on this result, lowest is the highest priority.
        # Results will be sorted by priority when submitting to the server.
        $Priority = 50,

        [Parameter()]
        [switch]
        # Remove any existing queued service results for this hostname/service
        # combination
        $ClearExistingQueue
    )

    Trap
    {
        throw $_
    }

    if ( [string]::IsNullOrWhiteSpace($HostName) )
    {
        throw [InvalidOperationException]'No host name specified.'
    }
    if ( [string]::IsNullOrWhiteSpace($ServiceName) )
    {
        throw [InvalidOperationException]'No service name specified.'
    }

    if ( $script:SocqSession.ServiceResults.ContainsKey($HostName) -eq $false )
    {
        $script:SocqSession.ServiceResults[$HostName] = @{}
    }
    if ( $ClearExistingQueue -or $script:SocqSession.ServiceResults.$HostName.ContainsKey($ServiceName) -eq $false )
    {
        $script:SocqSession.ServiceResults[$HostName][$ServiceName] = [Collections.ArrayList]::new()
    }
    $msg = $Output -replace "`r`n", "`n"
    $msg = $msg -replace "`r", "`n"
    $msg = $msg -replace "<br */?>", "`n"

    $res = [PSCustomObject]@{
        Id = $script:SocqSession.NextId++
        HostName = $HostName.ToLower()
        ServiceName = $ServiceName.ToUpper()
        Status = $Status
        Output = $Output
        Priority = $Priority
    }
    [void]$script:SocqSession.ServiceResults[$HostName][$ServiceName].Add($res)
} # function Add-SDSOCQServiceResult

function Clear-SDSOCQServiceResult
{
    <#
    .SYNOPSIS

    Clear all queued service results that have not yet been submitted to the server.

    .DESCRIPTION

    Removes all pending service results from the queue.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([void])]
    Param
    (
    )

    Trap
    {
        throw $_
    }

    $script:SocqSession.ServiceResults = @{}
} # function Clear-SDSOCQServiceResult

function Get-SDSOCQServiceResult
{
    <#
    .SYNOPSIS

    Get all service results pending submission to the server.

    .DESCRIPTION

    Get all service results pending submission to the server.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([Object[]])]
    Param
    (
    )

    Trap
    {
        throw $_
    }

    # Make sure the pipeline is blocked so that Get | Remove will work.
    $block = $script:SocqSession.ServiceResults.Values.Values | ForEach-Object { $_ }
    $block
} # function Get-SDSOCQServiceResult

function Initialize-SDSOCQ
{
    <#
    .SYNOPSIS

    Prepare the module for use.

    .DESCRIPTION

    Provide connection details and other initialization parameters. Must be
    called before any of the other cmdlets in this module.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([void])]
    Param
    (
        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [string]
        # The SOC server host name. i.e. soc.deakin.edu.au
        $SocServerHost = 'soc.deakin.edu.au',

        [Parameter(Mandatory = $true)]
        [ValidateNotNull()]
        [System.Management.Automation.Credential()]
        [Management.Automation.PSCredential]
        # SOCQ API credentials
        $Credential,

        [Parameter()]
        [string]
        # The default host name to use if not specified when calling cmdlets
        $DefaultHostName = $null,

        [Parameter()]
        [string]
        # The default service name to use if not specified when calling cmdlets
        $DefaultServiceName = $null
    )

    Trap
    {
        throw $_
    }

    $script:SocqSession.ServerHost      = $SocServerHost
    $script:SocqSession.Credential      = $Credential
    $script:SocqSession.DefaultHostName = $DefaultHostName
    $script:SocqSession.DefaultService  = $DefaultServiceName

    Clear-SDSOCQServiceResult
} # function Initialize-SDSOCQ

function Register-SDSOCQService
{
    <#
    .SYNOPSIS

    Create a new service definition.

    .DESCRIPTION

    Create a new service definition.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([void])]
    Param
    (
        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [string]
        # The name of the host as defined in the SOC service definition.
        # If not specified will use the DefaultHostName provided on
        # initialization.
        $HostName = $script:SocqSession.DefaultHostName,

        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [Alias('ServiceDefinition')]
        [string]
        # The name of the service definition.
        # If not specified will use the DefaultServiceName provided on
        # initialization.
        $ServiceName = $script:SocqSession.DefaultService,

        [Parameter()]
        [ValidateSet('TechGroup', 'NHS', 'EHS')]
        # Notification level
        #   TechGroup       Technical group
        #   NHS             Normal hour support
        #   EHS             Extended hour support
        $Notify = 'TechGroup',

        [Parameter()]
        [ValidateSet('ServiceDesk', 'ServiceDesk-TL', 'ServiceDesk-Optimisation')]
        $TechnicalGroup = 'ServiceDesk-Optimisation',

        [Parameter()]
        [ValidateRange('00:05:00', '31.00:00:00')]
        [TimeSpan]
        # How long without any results being submitted does the service take
        # to change to UNKNOWN status
        $FreshnessThreshold = [TimeSpan]::FromHours(1),

        [Parameter()]
        [Uri]
        # Link to help or other notes to help document the service.
        $NotesURI
    )

    Trap
    {
        throw $_
    }

    if ( [string]::IsNullOrEmpty($script:SocqSession.ServerHost) )
    {
        throw [InvalidOperationException]'Call Initialize-SDSOCQ before submitting results.'
    }

    $contactGroups = @()
    $use = @()
    switch ( $Notify )
    {
        'TechGroup'
        {
            $use += 'NOTIFY-TECH_GROUP'
        }
        'NHS'
        {
            $use += 'NOTIFY-NHS'
            $contactGroups += 'soc-holding'
        }
        'EHS'
        {
            $use += 'NOTIFY-EHS'
            $contactGroups += 'soc-holding'
        }
        default
        {
            throw [NotImplementedException]'Unknown notify level'
        }
    }
    switch ( $TechnicalGroup )
    {
        'ServiceDesk'
        {
            $use += 'TECH_GROUP-SERVICEDESK'
            $contactGroups = @('service_desk') + $contactGroups
            $socView = 'service_desk'
        }
        'ServiceDesk-TL'
        {
            $use += 'TECH_GROUP-SERVICE_DESK_TL'
            $contactGroups = @('service_desk_tl_admins') + $contactGroups
            $socView = 'service_desk_tl_admins'
        }
        'ServiceDesk-Optimisation'
        {
            $use += 'TECH_GROUP-SERVICEDESK_OPT'
            $contactGroups = @('service_desk_tl_admins') + $contactGroups
            $socView = 'service_desk_tl_admins'
        }
        default
        {
            throw [NotImplementedException]'Unknown technical group'
        }
    }
    $use += 'CHECK-PASSIVE', 'DEFAULTS'

    $action =
    @{
        'use'                   = $use -join ','
        'host_name'             = $HostName.ToLower()
        'service_description'   = $ServiceName.ToUpper()
        '_SOCVIEW'              = $socView
        'freshness_threshold'   = [int]$FreshnessThreshold.TotalSeconds
        'contact_groups'        = $contactGroups -join ','
    }

    if ( $null -ne $NotesURI -and $NotesURI.IsAbsoluteUri )
    {
        $action['notes_url'] = $NotesURI.ToString()
    }

    # Submit to SOCQ API
    $uri = 'https://{0}/socq/api/v0.01/object/services' -f $script:SocqSession.ServerHost
    $payload = $action | ConvertTo-Json
    $params =
    @{
        Uri         = $uri
        Method      = 'POST'
        Body        = $payload
        Credential  = $script:SocqSession.Credential
        ContentType = 'application/json'
    }
    Write-Verbose (
        'Calling {0} {1} {2}' -f
            $params.Method,
            $params.Uri,
            $payload
    )
    $result = Invoke-RestMethod @params
    Write-Verbose ('Result ' + ($result | ConvertTo-Json))
} # function Register-SDSOCQService

function Remove-SDSOCQServiceResult
{
    <#
    .SYNOPSIS

    Remove a service result from the queue to be submitted to the server.

    .DESCRIPTION

    Remove a service result from the queue to be submitted to the server.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSUseShouldProcessForStateChangingFunctions')]
    [OutputType([void])]
    Param
    (
        [Parameter(Mandatory = $true, ValueFromPipelineByPropertyName = $true)]
        [ValidateNotNullOrEmpty()]
        # The Id of the service result to remove.
        $Id
    )

    Begin
    {
        Trap
        {
            throw $_
        }
        $srs = @(Get-SDSOCQServiceResult)
    }
    Process
    {
        Trap
        {
            throw $_
        }

        $obj = $srs | Where-Object Id -EQ $Id

        if ( $null -ne $obj )
        {
            $script:SocqSession.ServiceResults.($obj.HostName).($obj.ServiceName).Remove($obj)
        }
    }
} # function Remove-SDSOCQServiceResult

function Set-SDSOCQServiceResult
{
    <#
    .SYNOPSIS

    Immediately submit a service result to the server.

    .DESCRIPTION

    Set a service result immediately. Clears and does not submit any previously
    queued entries for the hostname/service combination.

    #>
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'None')]
    [OutputType([void])]
    Param
    (
        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [string]
        # The name of the host as defined in the SOC service definition.
        # If not specified will use the DefaultHostName provided on
        # initialization.
        $HostName = $script:SocqSession.DefaultHostName,

        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [Alias('ServiceDefinition')]
        [string]
        # The name of the service definition.
        # If not specified will use the DefaultServiceName provided on
        # initialization.
        $ServiceName = $script:SocqSession.DefaultService,

        [Parameter(Mandatory = $true)]
        [ValidateSet('Ok', 'Warning', 'Critical', 'Unknown')]
        [string]
        # The status of the result to submit.
        $Status,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]
        # The text output to submit to SOC
        $Output
    )

    Trap
    {
        throw $_
    }

    Add-SDSOCQServiceResult @PSBoundParameters -ClearExistingQueue
    Submit-SDSOCQServiceResult
} # function Set-SDSOCQServiceResult

function Submit-SDSOCQServiceResult
{
    <#
    .SYNOPSIS

    Submit all queued service results to the server.

    .DESCRIPTION

    Submit all queued service results to the server.

    #>
    [CmdletBinding(SupportsShouldProcess = $true, ConfirmImpact = 'None')]
    [OutputType([void])]
    Param
    (
    )

    Trap
    {
        throw $_
    }

    if ( [string]::IsNullOrEmpty($script:SocqSession.ServerHost) )
    {
        throw [InvalidOperationException]'Call Initialize-SDSOCQ before submitting results.'
    }

    $hostcount = $script:SocqSession.ServiceResults.PSBase.Keys.Count
    if ( $hostcount -eq 0 )
    {
        return
    }

    $statusMap =
    @{
        'Ok' = 0
        'Warning' = 1
        'Critical' = 2
        'Unknown' = 3
    }
    $statusMsgMap =
    @{
        'Ok' = 'OK'
        'Warning' = 'WARN'
        'Critical' = 'CRIT'
        'Unknown' = 'UNKN'
    }
    $sortOrder = @(
        @{ Expression = { $statusMap[$_.Status] }; Descending = $true }
        'Priority'
    )
    $actions = [Collections.ArrayList]::new()
    foreach ( $hostname in $script:SocqSession.ServiceResults.PSBase.Keys )
    {
        foreach ( $servicename in $script:SocqSession.ServiceResults.$hostname.PSBase.Keys )
        {
            $msg = ''
            $results =
            @(
                $script:SocqSession.ServiceResults.$hostname.$servicename |
                    Sort-Object -Property:$sortOrder
            )
            $overallStatus = 0
            $msg =
            @(
                $results |
                    ForEach-Object {
                        '{0}: {1}' -f $statusMsgMap[$_.Status], $_.Output
                        $status = $statusMap[$_.Status]
                        if ( $status -gt $overallStatus )
                        {
                            $overallStatus = $status
                        }
                    }
                ) -join "`n"

            $payload =
            @{
                'host_name' = $hostname
                'service_description' = $servicename
                'status_code' = $overallStatus
                'plugin_output' = $msg -replace "`n", "<br>"
            }
            [void]$actions.Add($payload)
        }
    }

    $msg = 'Submit {0} results' -f $actions.Count
    $shouldProcess = $psCmdlet.ShouldProcess($script:SocqSession.ServerHost, $msg)
    foreach ( $action in $actions )
    {
        $msg = '{0}/{1}: {2}' -f
            $action.host_name,
            $action.service_description,
            $action.plugin_output -replace "<br>", "`n    "
        $msg | Out-Default

        if ( -not $shouldProcess )
        {
            continue
        }

        # Submit result to SOCQ API
        $uri = 'https://{0}/socq/api/v0.01/result/services' -f $script:SocqSession.ServerHost
        $payload = $action | ConvertTo-Json
        $params =
        @{
            Uri         = $uri
            Method      = 'POST'
            Body        = $payload
            Credential  = $script:SocqSession.Credential
            ContentType = 'application/json'
        }

        $maxAttempts = 3; $sleepBetweenAttempts = [TimeSpan]::FromSeconds(15)
        :nextAttempt for ( $attempt = 1; $attempt -le $maxAttempts; $attempt++ )
        {
            try
            {
                Invoke-RestMethod @params | Out-Null
            }
            catch
            {
                if
                (
                    $attempt -lt $maxAttempts -and
                    (
                        $_.Exception.Message -match 'Unable to read data from the transport connection' -or
                        $_.Exception.Message -match 'The response ended prematurely'
                    )
                )
                {
                    Write-Warning ('[Retry {0}/{1}] {2}' -f $attempt, $maxAttempts, $_.Exception.Message)
                    Write-Warning ('Sleeping {0}...' -f $sleepBetweenAttempts.ToString())
                    Start-Sleep -Seconds:$sleepBetweenAttempts.TotalSeconds
                    continue nextAttempt
                }
                else
                {
                    Write-Warning (
                        '{0} at {1}:{2}' -f
                            $_.Exception.Message,
                            $_.InvocationInfo.ScriptName,
                            $_.InvocationInfo.ScriptLineNumber
                    )
                    if ( $null -ne $_.ErrorDetails )
                    {
                        Write-Warning $_.ErrorDetails.Message
                    }
                    throw 'Unexpected result from SOCQ API'
                }
            }
            break nextAttempt
        }
    }

    Clear-SDSOCQServiceResult
} # function Submit-SDSOCQServiceResult

function Unregister-SDSOCQService
{
    <#
    .SYNOPSIS

    Delete an existing service definition.

    .DESCRIPTION

    Delete an existing service definition.

    #>
    [CmdletBinding(SupportsShouldProcess = $false, ConfirmImpact = 'None')]
    [OutputType([void])]
    Param
    (
        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [string]
        # The name of the host as defined in the SOC service definition.
        # If not specified will use the DefaultHostName provided on
        # initialization.
        $HostName = $script:SocqSession.DefaultHostName,

        [Parameter()]
        [ValidateNotNullOrEmpty()]
        [Alias('ServiceDefinition')]
        [string]
        # The name of the service definition.
        # If not specified will use the DefaultServiceName provided on
        # initialization.
        $ServiceName = $script:SocqSession.DefaultService
    )

    Trap
    {
        throw $_
    }

    $payload = "{
    ""services"": [
        [
            ""$($HostName.ToLower())"",
            ""$($ServiceName.ToUpper())""
        ]
    ]
}"

    # Submit to SOCQ API
    $uri = 'https://{0}/socq/api/v0.01/delete/services' -f $script:SocqSession.ServerHost
    $params =
    @{
        Uri         = $uri
        Method      = 'POST'
        Body        = $payload
        Credential  = $script:SocqSession.Credential
        ContentType = 'application/json'
    }
    Write-Verbose (
        'Calling {0} {1} {2}' -f
            $params.Method,
            $params.Uri,
            $payload
    )
    $result = Invoke-RestMethod @params
    Write-Verbose ('Result ' + ($result | ConvertTo-Json))
} # function Unregister-SDSOCQService

#endregion
#
#region Private helper functions
#
#
#endregion
#
#region Private module variables
$script:SocqSession =
@{
    ServerHost      = $null
    Credential      = $null
    DefaultHostName = $null
    DefaultService  = $null
    NextId          = 1
    ServiceResults  = @{}
}
#endregion
#
#region Export members
#
#
#Requires -Version:5.0
Set-StrictMode -Version:5
$script:ErrorActionPreference = 'Stop'

Export-ModuleMember -Function:'Add-SDSOCQServiceResult'
Export-ModuleMember -Function:'Clear-SDSOCQServiceResult'
Export-ModuleMember -Function:'Get-SDSOCQServiceResult'
Export-ModuleMember -Function:'Initialize-SDSOCQ'
Export-ModuleMember -Function:'Register-SDSOCQService'
Export-ModuleMember -Function:'Remove-SDSOCQServiceResult'
Export-ModuleMember -Function:'Set-SDSOCQServiceResult'
Export-ModuleMember -Function:'Submit-SDSOCQServiceResult'
Export-ModuleMember -Function:'Unregister-SDSOCQService'

#endregion
