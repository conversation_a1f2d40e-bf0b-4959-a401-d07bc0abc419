<#
.SYNOPSIS

Ensures the realtime PureCloud BI adapter is working

.DESCRIP<PERSON>ON

Queries the database to ensure the data is current.

#>
[CmdletBinding(SupportsShouldProcess = $false)]
Param
(
)

Trap
{
    throw $_
}

#Requires -Version:7
Set-StrictMode -Version:6
$ErrorActionPreference = 'Stop'
Import-Module -Name:"${PSScriptRoot}\..\modules\SDSecretManagement" -Verbose:$false
Import-Module -Name:"${PSScriptRoot}\..\modules\SDSOCQ" -Verbose:$false
Import-Module -Name:"${PSScriptRoot}\..\modules\SDDBMSSQL" -Verbose:$false

$config = Import-LocalizedData

$socCreds = Unprotect-SDSecret -Content:$config.SocqPass -UserName:$config.SocqUser
Initialize-SDSOCQ -Credential:$socCreds -DefaultHostname:'pcc-mssql-f1.du'

$queries = $config.Queries

try
{
    foreach ( $database in $config.Databases.psbase.Keys )
    {
        $dbConfig = $config.Databases[$database]
        $warningLag = [TimeSpan]$dbConfig.WarningLag
        $criticalLag = [TimeSpan]$dbConfig.CriticalLag
        Connect-SDMSSQLDatabase -InstanceName:'localhost' -DefaultDatabase:$database | Out-Null
        foreach ( $tableName in $queries.psbase.Keys )
        {
            $query = $queries[$tableName]
            $res = Invoke-SDMSSQLQuery -Query:$query
            $lag = [TimeSpan]::MaxValue
            if ( $null -ne $res.$tableName )
            {
                $lag = [DateTime]::Now - $res.$tableName.ToLocalTime()
            }

            $level = 'Ok'
            if ( $lag -gt $criticalLag )
            {
                $level = 'Critical'
            }
            elseif ( $lag -gt $warningLag )
            {
                $level = 'Warning'
            }

            $msg = $tableName + ' has no data'
            if ( $lag -ne [TimeSpan]::MaxValue )
            {
                $msg = '{0} last data update {1} ago ({2:N1} secs)' -f
                    $tableName,         # 0
                    $lag.ToString(),    # 1
                    $lag.TotalSeconds   # 2
            }
            Add-SDSOCQServiceResult -ServiceName:$dbConfig.SocService -Status:$level -Output:$msg
        }
    }
}
finally
{
    Submit-SDSOCQServiceResult
}
